"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardRenderer = void 0;
var react_1 = __importDefault(require("react"));
var types_1 = require("./types");
var templates_1 = require("./templates");
var cardMap = (_a = {},
    _a[types_1.CardTemplate.Default] = templates_1.DefaultCard,
    _a[types_1.CardTemplate.WithImage] = templates_1.WithImageCard,
    _a[types_1.CardTemplate.WithHeader] = templates_1.WithHeaderCard,
    _a);
var CardRenderer = function (props) {
    var CardComponent = cardMap[props.template];
    return react_1.default.createElement(CardComponent, __assign({}, props));
};
exports.CardRenderer = CardRenderer;
