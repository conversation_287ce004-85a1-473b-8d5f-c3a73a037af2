import React, { PropsWithChildren } from "react";
import { CardProps } from "./types";
interface CardSelectorProps {
    onCardSelect: (params: CardProps & {
        icon?: string;
        bg?: string;
    }) => void;
    initialParams?: CardProps & {
        icon?: string;
        bg?: string;
    };
    onCancel?: () => void;
}
export declare const CardSelector: ({ onCardSelect, initialParams, onCancel, }: CardSelectorProps & PropsWithChildren) => React.JSX.Element;
export {};
