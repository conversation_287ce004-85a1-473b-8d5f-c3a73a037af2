import React, { PropsWithChildren } from "react";
type CardIcon = 'book' | 'star' | 'palette';
interface CardSelectorProps {
    onCardSelect: (params: {
        icon: CardIcon;
        title: string;
        content: string[];
        bg: string;
    }) => void;
    initialParams?: {
        icon: CardIcon;
        title: string;
        content: string[];
        bg: string;
    };
    onCancel?: () => void;
}
export declare const CardSelector: ({ onCardSelect, initialParams, onCancel, }: CardSelectorProps & PropsWithChildren) => React.JSX.Element;
export {};
