"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardSelector = void 0;
var react_1 = __importStar(require("react"));
var lucide_react_1 = require("lucide-react");
var react_colorful_1 = require("react-colorful");
var types_1 = require("./types");
var button_1 = require("./components/ui/button");
var input_1 = require("./components/ui/input");
var label_1 = require("./components/ui/label");
var textarea_1 = require("./components/ui/textarea");
var select_1 = require("./components/ui/select");
var card_1 = require("./components/ui/card");
var CardSelector = function (_a) {
    var onCardSelect = _a.onCardSelect, initialParams = _a.initialParams, onCancel = _a.onCancel;
    var cardData = [
        {
            type: "book",
            icon: "book",
            title: "Sách hay",
            content: ["Tài liệu học tập", "Kiến thức nền tảng"],
            bg: "linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)",
            template: types_1.CardTemplate.Default,
        },
        {
            type: "star",
            icon: "star",
            title: "Nổi bật",
            content: ["Bài học nổi bật", "Gợi ý cho bạn"],
            bg: "linear-gradient(135deg, #fff7ae 0%, #ffd6e0 100%)",
            template: types_1.CardTemplate.WithImage,
        },
        {
            type: "palette",
            icon: "palette",
            title: "Sáng tạo",
            content: ["Tùy chỉnh nội dung", "Thỏa sức sáng tạo"],
            bg: "linear-gradient(135deg, #d1f5ee 0%, #f0e7ff 100%)",
            template: types_1.CardTemplate.WithHeader,
        },
    ];
    var iconMap = {
        book: react_1.default.createElement(lucide_react_1.Book, { size: 32 }),
        star: react_1.default.createElement(lucide_react_1.Star, { size: 32 }),
        palette: react_1.default.createElement(lucide_react_1.Palette, { size: 32 }),
    };
    // Nếu có initialParams thì dùng để edit, còn không thì chọn mới
    var _b = (0, react_1.useState)(function () {
        if (initialParams) {
            // Find the index of the card that matches the template
            var matchingIndex = cardData.findIndex(function (card) { return card.template === initialParams.template; });
            return matchingIndex >= 0 ? matchingIndex : 0;
        }
        return null;
    }), selectedIdx = _b[0], setSelectedIdx = _b[1];
    // Helper function to get default values based on template
    var getDefaultsForTemplate = function (template) {
        var _a;
        var templateDefaults = (_a = {},
            _a[types_1.CardTemplate.Default] = {
                icon: "book",
                bg: "linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)",
            },
            _a[types_1.CardTemplate.WithImage] = {
                icon: "star",
                bg: "linear-gradient(135deg, #fff7ae 0%, #ffd6e0 100%)",
            },
            _a[types_1.CardTemplate.WithHeader] = {
                icon: "palette",
                bg: "linear-gradient(135deg, #d1f5ee 0%, #f0e7ff 100%)",
            },
            _a);
        return templateDefaults[template];
    };
    var _c = (0, react_1.useState)(function () {
        if (initialParams) {
            var defaults = getDefaultsForTemplate(initialParams.template);
            return {
                icon: initialParams.icon || defaults.icon,
                title: initialParams.title,
                content: [initialParams.content], // Convert string to array
                bg: initialParams.bg || defaults.bg,
                template: initialParams.template,
            };
        }
        else {
            return {
                icon: "book",
                title: "",
                content: [""],
                bg: "#f8fafc",
                template: types_1.CardTemplate.Default,
            };
        }
    }), params = _c[0], setParams = _c[1];
    // Khi chọn card, set params theo card đó
    var handleSelect = function (idx) {
        setSelectedIdx(idx);
        setParams({
            icon: cardData[idx].icon,
            title: cardData[idx].title,
            content: __spreadArray([], cardData[idx].content, true),
            bg: cardData[idx].bg,
            template: cardData[idx].template,
        });
    };
    // Khi xác nhận, truyền params ra ngoài
    var handleConfirm = function () {
        // Convert CardParams to CardProps with extra styling data
        var cardProps = {
            template: params.template,
            title: params.title,
            content: params.content.join(", "), // Convert array to string
            icon: params.icon,
            bg: params.bg,
        };
        onCardSelect(cardProps);
    };
    // Khi bấm Cancel, gọi hàm onCancel nếu có
    var handleCancel = function () {
        if (onCancel)
            onCancel();
    };
    return (react_1.default.createElement("div", { style: { padding: "16px" } }, selectedIdx === null ? (react_1.default.createElement("div", { style: { display: "flex", justifyContent: "center", gap: "16px" } }, cardData.map(function (card, idx) { return (react_1.default.createElement("div", { key: card.type, onClick: function () { return handleSelect(idx); }, style: {
            border: "1px solid #ccc",
            borderRadius: "8px",
            padding: "16px",
            cursor: "pointer",
            textAlign: "center",
            background: card.bg,
        } },
        react_1.default.createElement("div", { style: { marginBottom: "8px" } }, iconMap[card.icon]),
        react_1.default.createElement("div", { style: {
                fontWeight: "bold",
                fontSize: "1.125rem",
                marginBottom: "8px",
            } }, card.title),
        react_1.default.createElement("ul", { style: { margin: 0, padding: 0, listStyle: "none" } }, card.content.map(function (c, i) { return (react_1.default.createElement("li", { key: i, style: {
                marginBottom: "4px",
                fontSize: "0.875rem",
                color: "#6b7280",
            } }, c)); })))); }))) : (react_1.default.createElement("div", { style: { margin: "0 auto", maxWidth: "800px" } },
        react_1.default.createElement("h3", { style: { textAlign: "center", marginBottom: "16px" } }, "Ch\u1EC9nh s\u1EEDa Card"),
        react_1.default.createElement("div", { style: {
                display: "flex",
                flexDirection: "row-reverse",
                gap: "20px",
                alignItems: "flex-start",
            } },
            react_1.default.createElement("div", { style: {
                    border: "1px solid #ccc",
                    borderRadius: "8px",
                    padding: "16px",
                    textAlign: "center",
                    background: params.bg,
                } },
                react_1.default.createElement("div", { style: { marginBottom: "8px" } }, iconMap[params.icon]),
                react_1.default.createElement("div", { style: {
                        fontWeight: "bold",
                        fontSize: "1.125rem",
                        marginBottom: "8px",
                    } }, params.title),
                react_1.default.createElement("ul", { style: { margin: 0, padding: 0, listStyle: "none" } }, params.content.map(function (c, i) { return (react_1.default.createElement("li", { key: i, style: {
                        marginBottom: "4px",
                        fontSize: "0.875rem",
                        color: "#6b7280",
                    } }, c)); }))),
            react_1.default.createElement(card_1.Card, { className: "w-full max-w-md" },
                react_1.default.createElement(card_1.CardHeader, null,
                    react_1.default.createElement(card_1.CardTitle, { className: "text-lg" }, "Ch\u1EC9nh s\u1EEDa Card")),
                react_1.default.createElement(card_1.CardContent, { className: "space-y-4" },
                    react_1.default.createElement("div", { className: "space-y-2" },
                        react_1.default.createElement(label_1.Label, { htmlFor: "icon" }, "Icon"),
                        react_1.default.createElement(select_1.Select, { value: params.icon, onValueChange: function (value) {
                                return setParams(function (p) { return (__assign(__assign({}, p), { icon: value })); });
                            } },
                            react_1.default.createElement(select_1.SelectTrigger, { id: "icon" },
                                react_1.default.createElement(select_1.SelectValue, { placeholder: "Ch\u1ECDn icon" })),
                            react_1.default.createElement(select_1.SelectContent, null,
                                react_1.default.createElement(select_1.SelectItem, { value: "book" }, "Book"),
                                react_1.default.createElement(select_1.SelectItem, { value: "star" }, "Star"),
                                react_1.default.createElement(select_1.SelectItem, { value: "palette" }, "Palette")))),
                    react_1.default.createElement("div", { className: "space-y-2" },
                        react_1.default.createElement(label_1.Label, { htmlFor: "title" }, "Title"),
                        react_1.default.createElement(input_1.Input, { id: "title", type: "text", value: params.title, onChange: function (e) {
                                return setParams(function (p) { return (__assign(__assign({}, p), { title: e.target.value })); });
                            }, placeholder: "Nh\u1EADp ti\u00EAu \u0111\u1EC1 card" })),
                    react_1.default.createElement("div", { className: "space-y-2" },
                        react_1.default.createElement(label_1.Label, { htmlFor: "content" }, "Content"),
                        react_1.default.createElement(textarea_1.Textarea, { id: "content", value: params.content.join("\n"), onChange: function (e) {
                                return setParams(function (p) { return (__assign(__assign({}, p), { content: e.target.value.split("\n") })); });
                            }, placeholder: "Nh\u1EADp n\u1ED9i dung card (m\u1ED7i d\u00F2ng m\u1ED9t m\u1EE5c)", className: "min-h-[80px]" })),
                    react_1.default.createElement("div", { className: "space-y-2" },
                        react_1.default.createElement(label_1.Label, { htmlFor: "background" }, "Background"),
                        react_1.default.createElement("div", { className: "space-y-2" },
                            react_1.default.createElement("div", { className: "flex justify-center p-4 border rounded-md" },
                                react_1.default.createElement(react_colorful_1.HexColorPicker, { color: params.bg, onChange: function (color) {
                                        return setParams(function (p) { return (__assign(__assign({}, p), { bg: color })); });
                                    } })),
                            react_1.default.createElement(input_1.Input, { id: "background", type: "text", value: params.bg, onChange: function (e) {
                                    return setParams(function (p) { return (__assign(__assign({}, p), { bg: e.target.value })); });
                                }, placeholder: "CSS background, v\u00ED d\u1EE5: #fff ho\u1EB7c linear-gradient(...)" }))),
                    react_1.default.createElement("div", { className: "flex gap-2 pt-4" },
                        react_1.default.createElement(button_1.Button, { onClick: handleConfirm, className: "flex-1" }, "L\u01B0u card n\u00E0y"),
                        onCancel && (react_1.default.createElement(button_1.Button, { variant: "outline", onClick: handleCancel }, "H\u1EE7y"))))))))));
};
exports.CardSelector = CardSelector;
