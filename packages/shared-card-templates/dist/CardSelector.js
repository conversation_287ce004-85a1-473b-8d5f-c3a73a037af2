"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardSelector = void 0;
var react_1 = __importStar(require("react"));
var lucide_react_1 = require("lucide-react");
var react_colorful_1 = require("react-colorful");
var CardSelector = function (_a) {
    var onCardSelect = _a.onCardSelect, initialParams = _a.initialParams, onCancel = _a.onCancel;
    var cardData = [
        {
            type: "book",
            icon: "book",
            title: "Sách hay",
            content: ["Tài liệu học tập", "Kiến thức nền tảng"],
            bg: "linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)",
        },
        {
            type: "star",
            icon: "star",
            title: "Nổi bật",
            content: ["Bài học nổi bật", "Gợi ý cho bạn"],
            bg: "linear-gradient(135deg, #fff7ae 0%, #ffd6e0 100%)",
        },
        {
            type: "palette",
            icon: "palette",
            title: "Sáng tạo",
            content: ["Tùy chỉnh nội dung", "Thỏa sức sáng tạo"],
            bg: "linear-gradient(135deg, #d1f5ee 0%, #f0e7ff 100%)",
        },
    ];
    var iconMap = {
        book: react_1.default.createElement(lucide_react_1.Book, { size: 32 }),
        star: react_1.default.createElement(lucide_react_1.Star, { size: 32 }),
        palette: react_1.default.createElement(lucide_react_1.Palette, { size: 32 }),
    };
    // Nếu có initialParams thì dùng để edit, còn không thì chọn mới
    var _b = (0, react_1.useState)(initialParams ? 0 : null), selectedIdx = _b[0], setSelectedIdx = _b[1];
    var _c = (0, react_1.useState)(initialParams || {
        icon: "book",
        title: "",
        content: [""],
        bg: "#f8fafc",
    }), params = _c[0], setParams = _c[1];
    // Khi chọn card, set params theo card đó
    var handleSelect = function (idx) {
        setSelectedIdx(idx);
        setParams({
            icon: cardData[idx].icon,
            title: cardData[idx].title,
            content: __spreadArray([], cardData[idx].content, true),
            bg: cardData[idx].bg,
        });
    };
    // Khi xác nhận, truyền params ra ngoài
    var handleConfirm = function () {
        onCardSelect(params);
    };
    // Khi bấm Cancel, gọi hàm onCancel nếu có
    var handleCancel = function () {
        if (onCancel)
            onCancel();
    };
    return (react_1.default.createElement("div", { style: { padding: '16px' } }, selectedIdx === null ? (react_1.default.createElement("div", { style: { display: 'flex', justifyContent: 'center', gap: '16px' } }, cardData.map(function (card, idx) { return (react_1.default.createElement("div", { key: card.type, onClick: function () { return handleSelect(idx); }, style: {
            border: '1px solid #ccc',
            borderRadius: '8px',
            padding: '16px',
            cursor: 'pointer',
            textAlign: 'center',
            background: card.bg,
        } },
        react_1.default.createElement("div", { style: { marginBottom: '8px' } }, iconMap[card.icon]),
        react_1.default.createElement("div", { style: { fontWeight: 'bold', fontSize: '1.125rem', marginBottom: '8px' } }, card.title),
        react_1.default.createElement("ul", { style: { margin: 0, padding: 0, listStyle: 'none' } }, card.content.map(function (c, i) { return (react_1.default.createElement("li", { key: i, style: { marginBottom: '4px', fontSize: '0.875rem', color: '#6b7280' } }, c)); })))); }))) : (react_1.default.createElement("div", { style: { margin: '0 auto', maxWidth: '800px' } },
        react_1.default.createElement("h3", { style: { textAlign: 'center', marginBottom: '16px' } }, "Ch\u1EC9nh s\u1EEDa Card"),
        react_1.default.createElement("div", { style: { display: 'flex', flexDirection: 'row-reverse', gap: '20px', alignItems: 'flex-start' } },
            react_1.default.createElement("div", { style: {
                    border: '1px solid #ccc',
                    borderRadius: '8px',
                    padding: '16px',
                    textAlign: 'center',
                    background: params.bg,
                } },
                react_1.default.createElement("div", { style: { marginBottom: '8px' } }, iconMap[params.icon]),
                react_1.default.createElement("div", { style: { fontWeight: 'bold', fontSize: '1.125rem', marginBottom: '8px' } }, params.title),
                react_1.default.createElement("ul", { style: { margin: 0, padding: 0, listStyle: 'none' } }, params.content.map(function (c, i) { return (react_1.default.createElement("li", { key: i, style: { marginBottom: '4px', fontSize: '0.875rem', color: '#6b7280' } }, c)); }))),
            react_1.default.createElement("div", { style: { display: 'flex', flexDirection: 'column', gap: '16px', padding: '20px', borderRadius: '8px', background: '#f9fafb', boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)' } },
                react_1.default.createElement("label", { style: { display: 'flex', flexDirection: 'column', gap: '4px', fontWeight: 'bold' } },
                    react_1.default.createElement("span", null, "Icon:"),
                    react_1.default.createElement("select", { value: params.icon, onChange: function (e) {
                            return setParams(function (p) { return (__assign(__assign({}, p), { icon: e.target.value })); });
                        }, style: { marginLeft: '8px', width: 'fit-content', borderRadius: '4px', border: '1px solid #ccc', padding: '4px 8px' } },
                        react_1.default.createElement("option", { value: "book" }, "Book"),
                        react_1.default.createElement("option", { value: "star" }, "Star"),
                        react_1.default.createElement("option", { value: "palette" }, "Palette"))),
                react_1.default.createElement("label", { style: { display: 'flex', flexDirection: 'column', gap: '4px', fontWeight: 'bold' } },
                    react_1.default.createElement("span", null, "Title:"),
                    react_1.default.createElement("input", { type: "text", value: params.title, onChange: function (e) {
                            return setParams(function (p) { return (__assign(__assign({}, p), { title: e.target.value })); });
                        }, style: { marginLeft: '8px', width: '80%', borderRadius: '4px', border: '1px solid #ccc', padding: '4px 8px' } })),
                react_1.default.createElement("label", { style: { display: 'flex', flexDirection: 'column', gap: '4px', fontWeight: 'bold' } },
                    react_1.default.createElement("span", null, "Content:"),
                    react_1.default.createElement("textarea", { value: params.content.join("\n"), onChange: function (e) {
                            return setParams(function (p) { return (__assign(__assign({}, p), { content: e.target.value.split("\n") })); });
                        }, rows: 3, style: { marginLeft: '8px', width: '80%', borderRadius: '4px', border: '1px solid #ccc', padding: '4px 8px' } })),
                react_1.default.createElement("label", { style: { display: 'flex', flexDirection: 'column', gap: '4px', fontWeight: 'bold' } },
                    react_1.default.createElement("span", null, "Background:"),
                    react_1.default.createElement("div", { style: { margin: '8px 0 8px 8px' } },
                        react_1.default.createElement(react_colorful_1.HexColorPicker, { color: params.bg, onChange: function (color) {
                                return setParams(function (p) { return (__assign(__assign({}, p), { bg: color })); });
                            } })),
                    react_1.default.createElement("input", { type: "text", value: params.bg, onChange: function (e) {
                            return setParams(function (p) { return (__assign(__assign({}, p), { bg: e.target.value })); });
                        }, style: { marginTop: '8px', width: '80%', borderRadius: '4px', border: '1px solid #ccc', padding: '4px 8px' }, placeholder: "CSS background, v\u00ED d\u1EE5: #fff ho\u1EB7c linear-gradient(...)" })),
                react_1.default.createElement("div", { style: { marginTop: '12px', display: 'flex', gap: '8px' } },
                    react_1.default.createElement("button", { type: "button", onClick: handleConfirm, style: { padding: '8px 16px', background: '#3b82f6', color: 'white', borderRadius: '6px' } }, "L\u01B0u card n\u00E0y"),
                    onCancel && (react_1.default.createElement("button", { type: "button", onClick: handleCancel, style: { padding: '8px 16px', background: '#e5e7eb', color: '#374151', borderRadius: '6px' } }, "H\u1EE7y")))))))));
};
exports.CardSelector = CardSelector;
var cardData = [
    {
        type: "book",
        icon: "book",
        title: "Sách hay",
        content: ["Tài liệu học tập", "Kiến thức nền tảng"],
        bg: "linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)",
    },
    {
        type: "star",
        icon: "star",
        title: "Nổi bật",
        content: ["Bài học nổi bật", "Gợi ý cho bạn"],
        bg: "linear-gradient(135deg, #fff7ae 0%, #ffd6e0 100%)",
    },
    {
        type: "palette",
        icon: "palette",
        title: "Sáng tạo",
        content: ["Tùy chỉnh nội dung", "Thỏa sức sáng tạo"],
        bg: "linear-gradient(135deg, #d1f5ee 0%, #f0e7ff 100%)",
    },
];
var iconMap = {
    book: react_1.default.createElement(lucide_react_1.Book, { size: 32 }),
    star: react_1.default.createElement(lucide_react_1.Star, { size: 32 }),
    palette: react_1.default.createElement(lucide_react_1.Palette, { size: 32 }),
};
