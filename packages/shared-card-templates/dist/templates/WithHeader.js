"use strict";
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WithHeaderCard = void 0;
var react_1 = __importDefault(require("react"));
var lucide_react_1 = require("lucide-react");
var iconMap = {
    book: react_1.default.createElement(lucide_react_1.Book, { size: 32 }),
    star: react_1.default.createElement(lucide_react_1.Star, { size: 32 }),
    palette: react_1.default.createElement(lucide_react_1.Palette, { size: 32 }),
};
var WithHeaderCard = function (_a) {
    var title = _a.title, content = _a.content, header = _a.header, props = __rest(_a, ["title", "content", "header"]);
    // Parse content if it's a string with commas (from CardSelector)
    var contentArray = typeof content === "string" ? content.split(", ") : [content];
    var icon = props.icon || "palette";
    var bg = props.bg || "linear-gradient(135deg, #d1f5ee 0%, #f0e7ff 100%)";
    return (react_1.default.createElement("div", { style: {
            border: "1px solid #ccc",
            borderRadius: "8px",
            background: bg,
            minWidth: "200px",
        } },
        header && (react_1.default.createElement("div", { style: {
                padding: "16px",
                borderBottom: "1px solid #ccc",
                textAlign: "center",
                fontWeight: "bold",
                fontSize: "0.875rem",
                color: "#374151",
            } }, header)),
        react_1.default.createElement("div", { style: { padding: "16px", textAlign: "center" } },
            react_1.default.createElement("div", { style: { marginBottom: "8px" } }, iconMap[icon]),
            react_1.default.createElement("div", { style: {
                    fontWeight: "bold",
                    fontSize: "1.125rem",
                    marginBottom: "8px",
                } }, title),
            react_1.default.createElement("ul", { style: { margin: 0, padding: 0, listStyle: "none" } }, contentArray.map(function (c, i) { return (react_1.default.createElement("li", { key: i, style: {
                    marginBottom: "4px",
                    fontSize: "0.875rem",
                    color: "#6b7280",
                } }, c)); })))));
};
exports.WithHeaderCard = WithHeaderCard;
