"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WithHeaderCard = void 0;
var react_1 = __importDefault(require("react"));
var WithHeaderCard = function (_a) {
    var title = _a.title, content = _a.content, header = _a.header;
    return (react_1.default.createElement("div", { style: { border: '1px solid #ccc', borderRadius: '8px' } },
        header && react_1.default.createElement("div", { style: { padding: '16px', borderBottom: '1px solid #ccc' } }, header),
        react_1.default.createElement("div", { style: { padding: '16px' } },
            react_1.default.createElement("h2", null, title),
            react_1.default.createElement("p", null, content))));
};
exports.WithHeaderCard = WithHeaderCard;
