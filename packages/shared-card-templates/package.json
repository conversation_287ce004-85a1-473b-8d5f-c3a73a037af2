{"name": "@aicademy/shared-card-templates", "version": "0.1.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc"}, "peerDependencies": {"react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "typescript": "^5.0.0"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.542.0", "react-colorful": "^5.6.1", "tailwind-merge": "^3.3.1"}}