import React from 'react';
import { CardTemplate, CardProps } from './types';
import { DefaultCard, WithImageCard, WithHeaderCard } from './templates';

const cardMap = {
  [CardTemplate.Default]: DefaultCard,
  [CardTemplate.WithImage]: WithImageCard,
  [CardTemplate.WithHeader]: WithHeaderCard,
};

export const CardRenderer = (props: CardProps) => {
  const CardComponent = cardMap[props.template];
  return <CardComponent {...props} />;
};
