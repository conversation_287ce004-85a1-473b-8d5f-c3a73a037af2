import React, { PropsWithChildren, useState } from "react";
import { Book, Palette, Star } from "lucide-react";
import { HexColorPicker } from "react-colorful";
import { CardTemplate, CardProps } from "./types";

type CardIcon = "book" | "star" | "palette";

interface CardParams {
  icon: CardIcon;
  title: string;
  content: string[];
  bg: string;
  template: CardTemplate;
}

interface CardSelectorProps {
  onCardSelect: (params: CardProps) => void;
  initialParams?: CardProps;
  onCancel?: () => void;
}

export const CardSelector = ({
  onCardSelect,
  initialParams,
  onCancel,
}: CardSelectorProps & PropsWithChildren) => {
  const cardData: (CardParams & { type: CardIcon })[] = [
    {
      type: "book",
      icon: "book",
      title: "Sách hay",
      content: ["Tài liệu học tập", "Ki<PERSON>n thức nền tảng"],
      bg: "linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)",
      template: CardTemplate.Default,
    },
    {
      type: "star",
      icon: "star",
      title: "Nổi bật",
      content: ["Bài học nổi bật", "Gợi ý cho bạn"],
      bg: "linear-gradient(135deg, #fff7ae 0%, #ffd6e0 100%)",
      template: CardTemplate.WithImage,
    },
    {
      type: "palette",
      icon: "palette",
      title: "Sáng tạo",
      content: ["Tùy chỉnh nội dung", "Thỏa sức sáng tạo"],
      bg: "linear-gradient(135deg, #d1f5ee 0%, #f0e7ff 100%)",
      template: CardTemplate.WithHeader,
    },
  ];

  const iconMap: Record<CardIcon, React.ReactNode> = {
    book: <Book size={32} />,
    star: <Star size={32} />,
    palette: <Palette size={32} />,
  };

  // Nếu có initialParams thì dùng để edit, còn không thì chọn mới
  const [selectedIdx, setSelectedIdx] = useState<number | null>(
    initialParams ? 0 : null
  );
  const [params, setParams] = useState<CardParams>(
    initialParams
      ? {
          icon: "book", // Default icon
          title: initialParams.title,
          content: [initialParams.content], // Convert string to array
          bg: "#f8fafc", // Default bg
          template: initialParams.template,
        }
      : {
          icon: "book",
          title: "",
          content: [""],
          bg: "#f8fafc",
          template: CardTemplate.Default,
        }
  );

  // Khi chọn card, set params theo card đó
  const handleSelect = (idx: number) => {
    setSelectedIdx(idx);
    setParams({
      icon: cardData[idx].icon,
      title: cardData[idx].title,
      content: [...cardData[idx].content],
      bg: cardData[idx].bg,
      template: cardData[idx].template,
    });
  };

  // Khi xác nhận, truyền params ra ngoài
  const handleConfirm = () => {
    // Convert CardParams to CardProps
    const cardProps: CardProps = {
      template: params.template,
      title: params.title,
      content: params.content.join(", "), // Convert array to string
    };
    onCardSelect(cardProps);
  };

  // Khi bấm Cancel, gọi hàm onCancel nếu có
  const handleCancel = () => {
    if (onCancel) onCancel();
  };

  return (
    <div style={{ padding: "16px" }}>
      {/* Nếu chưa chọn card thì show danh sách card, nếu đã chọn thì chỉ show phần tùy chỉnh và preview */}
      {selectedIdx === null ? (
        <div style={{ display: "flex", justifyContent: "center", gap: "16px" }}>
          {cardData.map((card, idx) => (
            <div
              key={card.type}
              onClick={() => handleSelect(idx)}
              style={{
                border: "1px solid #ccc",
                borderRadius: "8px",
                padding: "16px",
                cursor: "pointer",
                textAlign: "center",
                background: card.bg,
              }}
            >
              <div style={{ marginBottom: "8px" }}>{iconMap[card.icon]}</div>
              <div
                style={{
                  fontWeight: "bold",
                  fontSize: "1.125rem",
                  marginBottom: "8px",
                }}
              >
                {card.title}
              </div>
              <ul style={{ margin: 0, padding: 0, listStyle: "none" }}>
                {card.content.map((c, i) => (
                  <li
                    key={i}
                    style={{
                      marginBottom: "4px",
                      fontSize: "0.875rem",
                      color: "#6b7280",
                    }}
                  >
                    {c}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      ) : (
        <div style={{ margin: "0 auto", maxWidth: "800px" }}>
          <h3 style={{ textAlign: "center", marginBottom: "16px" }}>
            Chỉnh sửa Card
          </h3>
          <div
            style={{
              display: "flex",
              flexDirection: "row-reverse",
              gap: "20px",
              alignItems: "flex-start",
            }}
          >
            {/* Preview card */}
            <div
              style={{
                border: "1px solid #ccc",
                borderRadius: "8px",
                padding: "16px",
                textAlign: "center",
                background: params.bg,
              }}
            >
              <div style={{ marginBottom: "8px" }}>{iconMap[params.icon]}</div>
              <div
                style={{
                  fontWeight: "bold",
                  fontSize: "1.125rem",
                  marginBottom: "8px",
                }}
              >
                {params.title}
              </div>
              <ul style={{ margin: 0, padding: 0, listStyle: "none" }}>
                {params.content.map((c, i) => (
                  <li
                    key={i}
                    style={{
                      marginBottom: "4px",
                      fontSize: "0.875rem",
                      color: "#6b7280",
                    }}
                  >
                    {c}
                  </li>
                ))}
              </ul>
            </div>
            {/* Edit form */}
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "16px",
                padding: "20px",
                borderRadius: "8px",
                background: "#f9fafb",
                boxShadow:
                  "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
              }}
            >
              <label
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "4px",
                  fontWeight: "bold",
                }}
              >
                <span>Icon:</span>
                <select
                  value={params.icon}
                  onChange={(e) =>
                    setParams((p) => ({
                      ...p,
                      icon: e.target.value as CardIcon,
                    }))
                  }
                  style={{
                    marginLeft: "8px",
                    width: "fit-content",
                    borderRadius: "4px",
                    border: "1px solid #ccc",
                    padding: "4px 8px",
                  }}
                >
                  <option value="book">Book</option>
                  <option value="star">Star</option>
                  <option value="palette">Palette</option>
                </select>
              </label>
              <label
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "4px",
                  fontWeight: "bold",
                }}
              >
                <span>Title:</span>
                <input
                  type="text"
                  value={params.title}
                  onChange={(e) =>
                    setParams((p) => ({ ...p, title: e.target.value }))
                  }
                  style={{
                    marginLeft: "8px",
                    width: "80%",
                    borderRadius: "4px",
                    border: "1px solid #ccc",
                    padding: "4px 8px",
                  }}
                />
              </label>
              <label
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "4px",
                  fontWeight: "bold",
                }}
              >
                <span>Content:</span>
                <textarea
                  value={params.content.join("\n")}
                  onChange={(e) =>
                    setParams((p) => ({
                      ...p,
                      content: e.target.value.split("\n"),
                    }))
                  }
                  rows={3}
                  style={{
                    marginLeft: "8px",
                    width: "80%",
                    borderRadius: "4px",
                    border: "1px solid #ccc",
                    padding: "4px 8px",
                  }}
                />
              </label>
              <label
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "4px",
                  fontWeight: "bold",
                }}
              >
                <span>Background:</span>
                <div style={{ margin: "8px 0 8px 8px" }}>
                  <HexColorPicker
                    color={params.bg}
                    onChange={(color) =>
                      setParams((p) => ({ ...p, bg: color }))
                    }
                  />
                </div>
                <input
                  type="text"
                  value={params.bg}
                  onChange={(e) =>
                    setParams((p) => ({ ...p, bg: e.target.value }))
                  }
                  style={{
                    marginTop: "8px",
                    width: "80%",
                    borderRadius: "4px",
                    border: "1px solid #ccc",
                    padding: "4px 8px",
                  }}
                  placeholder="CSS background, ví dụ: #fff hoặc linear-gradient(...)"
                />
              </label>
              <div style={{ marginTop: "12px", display: "flex", gap: "8px" }}>
                <button
                  type="button"
                  onClick={handleConfirm}
                  style={{
                    padding: "8px 16px",
                    background: "#3b82f6",
                    color: "white",
                    borderRadius: "6px",
                  }}
                >
                  Lưu card này
                </button>
                {onCancel && (
                  <button
                    type="button"
                    onClick={handleCancel}
                    style={{
                      padding: "8px 16px",
                      background: "#e5e7eb",
                      color: "#374151",
                      borderRadius: "6px",
                    }}
                  >
                    Hủy
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
