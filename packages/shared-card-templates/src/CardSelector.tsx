import React, { PropsWithChildren, useState } from "react";
import { Book, Palette, Star } from "lucide-react";
import { HexColorPicker } from "react-colorful";
import { CardTemplate, CardProps } from "./types";
import { Button } from "./components/ui/button";
import { Input } from "./components/ui/input";
import { Label } from "./components/ui/label";
import { Textarea } from "./components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "./components/ui/card";

type CardIcon = "book" | "star" | "palette";

interface CardParams {
  icon: CardIcon;
  title: string;
  content: string[];
  bg: string;
  template: CardTemplate;
}

interface CardSelectorProps {
  onCardSelect: (params: CardProps & { icon?: string; bg?: string }) => void;
  initialParams?: CardProps & { icon?: string; bg?: string };
  onCancel?: () => void;
}

export const CardSelector = ({
  onCardSelect,
  initialParams,
  onCancel,
}: CardSelectorProps & PropsWithChildren) => {
  const cardData: (CardParams & { type: CardIcon })[] = [
    {
      type: "book",
      icon: "book",
      title: "Sách hay",
      content: ["Tài liệu học tập", "Kiến thức nền tảng"],
      bg: "linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)",
      template: CardTemplate.Default,
    },
    {
      type: "star",
      icon: "star",
      title: "Nổi bật",
      content: ["Bài học nổi bật", "Gợi ý cho bạn"],
      bg: "linear-gradient(135deg, #fff7ae 0%, #ffd6e0 100%)",
      template: CardTemplate.WithImage,
    },
    {
      type: "palette",
      icon: "palette",
      title: "Sáng tạo",
      content: ["Tùy chỉnh nội dung", "Thỏa sức sáng tạo"],
      bg: "linear-gradient(135deg, #d1f5ee 0%, #f0e7ff 100%)",
      template: CardTemplate.WithHeader,
    },
  ];

  const iconMap: Record<CardIcon, React.ReactNode> = {
    book: <Book size={32} />,
    star: <Star size={32} />,
    palette: <Palette size={32} />,
  };

  // Nếu có initialParams thì dùng để edit, còn không thì chọn mới
  const [selectedIdx, setSelectedIdx] = useState<number | null>(() => {
    if (initialParams) {
      // Find the index of the card that matches the template
      const matchingIndex = cardData.findIndex(
        (card) => card.template === initialParams.template
      );
      return matchingIndex >= 0 ? matchingIndex : 0;
    }
    return null;
  });
  // Helper function to get default values based on template
  const getDefaultsForTemplate = (template: CardTemplate) => {
    const templateDefaults = {
      [CardTemplate.Default]: {
        icon: "book" as CardIcon,
        bg: "linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)",
      },
      [CardTemplate.WithImage]: {
        icon: "star" as CardIcon,
        bg: "linear-gradient(135deg, #fff7ae 0%, #ffd6e0 100%)",
      },
      [CardTemplate.WithHeader]: {
        icon: "palette" as CardIcon,
        bg: "linear-gradient(135deg, #d1f5ee 0%, #f0e7ff 100%)",
      },
    };
    return templateDefaults[template];
  };

  const [params, setParams] = useState<CardParams>(() => {
    if (initialParams) {
      const defaults = getDefaultsForTemplate(initialParams.template);
      return {
        icon: (initialParams as any).icon || defaults.icon,
        title: initialParams.title,
        content: [initialParams.content], // Convert string to array
        bg: (initialParams as any).bg || defaults.bg,
        template: initialParams.template,
      };
    } else {
      return {
        icon: "book",
        title: "",
        content: [""],
        bg: "#f8fafc",
        template: CardTemplate.Default,
      };
    }
  });

  // Khi chọn card, set params theo card đó
  const handleSelect = (idx: number) => {
    setSelectedIdx(idx);
    setParams({
      icon: cardData[idx].icon,
      title: cardData[idx].title,
      content: [...cardData[idx].content],
      bg: cardData[idx].bg,
      template: cardData[idx].template,
    });
  };

  const handleConfirm = () => {
    const cardProps: CardProps & { icon?: string; bg?: string } = {
      template: params.template,
      title: params.title,
      content: params.content.join(", "), // Convert array to string
      icon: params.icon,
      bg: params.bg,
    };
    onCardSelect(cardProps);
  };

  const handleCancel = () => {
    if (onCancel) onCancel();
  };

  return (
    <div className="p-4">
      {selectedIdx === null ? (
        <div className="flex justify-center gap-4">
          {cardData.map((card, idx) => (
            <div
              key={card.type}
              onClick={() => handleSelect(idx)}
              className="border border-gray-300 rounded-lg p-4 cursor-pointer text-center transition-transform hover:scale-105"
              style={{ background: card.bg }}
            >
              <div className="mb-2">{iconMap[card.icon]}</div>
              <div className="font-bold text-lg mb-2">{card.title}</div>
              <ul className="m-0 p-0 list-none">
                {card.content.map((c, i) => (
                  <li key={i} className="mb-1 text-sm text-gray-500">
                    {c}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      ) : (
        <div className="mx-auto max-w-4xl">
          <h3 className="text-center mb-4 text-xl font-semibold">
            Chỉnh sửa Card
          </h3>
          <div className="flex flex-row-reverse gap-5 items-start">
            {/* Preview card */}
            <div
              className="border border-gray-300 rounded-lg p-4 text-center"
              style={{ background: params.bg }}
            >
              <div className="mb-2">{iconMap[params.icon]}</div>
              <div className="font-bold text-lg mb-2">{params.title}</div>
              <ul className="m-0 p-0 list-none">
                {params.content.map((c, i) => (
                  <li key={i} className="mb-1 text-sm text-gray-500">
                    {c}
                  </li>
                ))}
              </ul>
            </div>
            {/* Edit form */}
            <Card className="w-full max-w-4xl">
              <CardHeader>
                <CardTitle className="text-lg">Chỉnh sửa Card</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="icon">Icon</Label>
                  <Select
                    value={params.icon}
                    onValueChange={(value: CardIcon) =>
                      setParams((p) => ({ ...p, icon: value }))
                    }
                  >
                    <SelectTrigger id="icon">
                      <SelectValue placeholder="Chọn icon" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="book">Book</SelectItem>
                      <SelectItem value="star">Star</SelectItem>
                      <SelectItem value="palette">Palette</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    type="text"
                    value={params.title}
                    onChange={(e) =>
                      setParams((p) => ({ ...p, title: e.target.value }))
                    }
                    placeholder="Nhập tiêu đề card"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="content">Content</Label>
                  <Textarea
                    id="content"
                    value={params.content.join("\n")}
                    onChange={(e) =>
                      setParams((p) => ({
                        ...p,
                        content: e.target.value.split("\n"),
                      }))
                    }
                    placeholder="Nhập nội dung card (mỗi dòng một mục)"
                    className="min-h-[80px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="background">Background</Label>
                  <div className="space-y-2">
                    <div className="flex justify-center p-4 border rounded-md">
                      <HexColorPicker
                        color={params.bg}
                        onChange={(color) =>
                          setParams((p) => ({ ...p, bg: color }))
                        }
                      />
                    </div>
                    <Input
                      id="background"
                      type="text"
                      value={params.bg}
                      onChange={(e) =>
                        setParams((p) => ({ ...p, bg: e.target.value }))
                      }
                      placeholder="CSS background, ví dụ: #fff hoặc linear-gradient(...)"
                    />
                  </div>
                </div>

                <div className="flex gap-2 pt-4">
                  <Button onClick={handleConfirm} className="flex-1">
                    Lưu card này
                  </Button>
                  {onCancel && (
                    <Button variant="outline" onClick={handleCancel}>
                      Hủy
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
};
