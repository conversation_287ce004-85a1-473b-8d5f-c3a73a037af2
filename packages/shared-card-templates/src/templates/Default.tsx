import React from "react";
import { Book, Palette, Star } from "lucide-react";
import { CardProps } from "../types";

const iconMap = {
  book: <Book size={32} />,
  star: <Star size={32} />,
  palette: <Palette size={32} />,
};

export const DefaultCard = ({
  title,
  content,
  ...props
}: CardProps & { icon?: "book" | "star" | "palette"; bg?: string }) => {
  // Parse content if it's a string with commas (from CardSelector)
  const contentArray =
    typeof content === "string" ? content.split(", ") : [content];
  const icon = (props as any).icon || "book";
  const bg =
    (props as any).bg || "linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)";

  return (
    <div
      style={{
        border: "1px solid #ccc",
        borderRadius: "8px",
        padding: "16px",
        textAlign: "center",
        background: bg,
        minWidth: "200px",
      }}
    >
      <div style={{ marginBottom: "8px" }}>
        {iconMap[icon as keyof typeof iconMap]}
      </div>
      <div
        style={{
          fontWeight: "bold",
          fontSize: "1.125rem",
          marginBottom: "8px",
        }}
      >
        {title}
      </div>
      <ul style={{ margin: 0, padding: 0, listStyle: "none" }}>
        {contentArray.map((c, i) => (
          <li
            key={i}
            style={{
              marginBottom: "4px",
              fontSize: "0.875rem",
              color: "#6b7280",
            }}
          >
            {c}
          </li>
        ))}
      </ul>
    </div>
  );
};
