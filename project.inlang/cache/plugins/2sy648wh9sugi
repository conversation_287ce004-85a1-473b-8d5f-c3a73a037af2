var Un=Object.create;var Xe=Object.defineProperty;var Pn=Object.getOwnPropertyDescriptor;var vn=Object.getOwnPropertyNames;var Nn=Object.getPrototypeOf,Sn=Object.prototype.hasOwnProperty;var Rn=(s,e)=>()=>(e||s((e={exports:{}}).exports,e),e.exports);var xn=(s,e,i,u)=>{if(e&&typeof e=="object"||typeof e=="function")for(let p of vn(e))!Sn.call(s,p)&&p!==i&&Xe(s,p,{get:()=>e[p],enumerable:!(u=Pn(e,p))||u.enumerable});return s};var jn=(s,e,i)=>(i=s!=null?Un(Nn(s)):{},xn(e||!s||!s.__esModule?Xe(i,"default",{value:s,enumerable:!0}):i,s));var he=Rn(o=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0});o.Type=o.JsonType=o.JavaScriptTypeBuilder=o.JsonTypeBuilder=o.TypeBuilder=o.TypeBuilderError=o.TransformEncodeBuilder=o.TransformDecodeBuilder=o.TemplateLiteralDslParser=o.TemplateLiteralGenerator=o.TemplateLiteralGeneratorError=o.TemplateLiteralFinite=o.TemplateLiteralFiniteError=o.TemplateLiteralParser=o.TemplateLiteralParserError=o.TemplateLiteralResolver=o.TemplateLiteralPattern=o.TemplateLiteralPatternError=o.UnionResolver=o.KeyArrayResolver=o.KeyArrayResolverError=o.KeyResolver=o.ObjectMap=o.Intrinsic=o.IndexedAccessor=o.TypeClone=o.TypeExtends=o.TypeExtendsResult=o.TypeExtendsError=o.ExtendsUndefined=o.TypeGuard=o.TypeGuardUnknownTypeError=o.ValueGuard=o.FormatRegistry=o.TypeBoxError=o.TypeRegistry=o.PatternStringExact=o.PatternNumberExact=o.PatternBooleanExact=o.PatternString=o.PatternNumber=o.PatternBoolean=o.Kind=o.Hint=o.Optional=o.Readonly=o.Transform=void 0;o.Transform=Symbol.for("TypeBox.Transform");o.Readonly=Symbol.for("TypeBox.Readonly");o.Optional=Symbol.for("TypeBox.Optional");o.Hint=Symbol.for("TypeBox.Hint");o.Kind=Symbol.for("TypeBox.Kind");o.PatternBoolean="(true|false)";o.PatternNumber="(0|[1-9][0-9]*)";o.PatternString="(.*)";o.PatternBooleanExact=`^${o.PatternBoolean}$`;o.PatternNumberExact=`^${o.PatternNumber}$`;o.PatternStringExact=`^${o.PatternString}$`;var Ve;(function(s){let e=new Map;function i(){return new Map(e)}s.Entries=i;function u(){return e.clear()}s.Clear=u;function p(y){return e.delete(y)}s.Delete=p;function l(y){return e.has(y)}s.Has=l;function c(y,b){e.set(y,b)}s.Set=c;function T(y){return e.get(y)}s.Get=T})(Ve||(o.TypeRegistry=Ve={}));var D=class extends Error{constructor(e){super(e)}};o.TypeBoxError=D;var Ze;(function(s){let e=new Map;function i(){return new Map(e)}s.Entries=i;function u(){return e.clear()}s.Clear=u;function p(y){return e.delete(y)}s.Delete=p;function l(y){return e.has(y)}s.Has=l;function c(y,b){e.set(y,b)}s.Set=c;function T(y){return e.get(y)}s.Get=T})(Ze||(o.FormatRegistry=Ze={}));var I;(function(s){function e(m){return Array.isArray(m)}s.IsArray=e;function i(m){return typeof m=="bigint"}s.IsBigInt=i;function u(m){return typeof m=="boolean"}s.IsBoolean=u;function p(m){return m instanceof globalThis.Date}s.IsDate=p;function l(m){return m===null}s.IsNull=l;function c(m){return typeof m=="number"}s.IsNumber=c;function T(m){return typeof m=="object"&&m!==null}s.IsObject=T;function y(m){return typeof m=="string"}s.IsString=y;function b(m){return m instanceof globalThis.Uint8Array}s.IsUint8Array=b;function g(m){return m===void 0}s.IsUndefined=g})(I||(o.ValueGuard=I={}));var ze=class extends D{};o.TypeGuardUnknownTypeError=ze;var a;(function(s){function e(r){try{return new RegExp(r),!0}catch{return!1}}function i(r){if(!I.IsString(r))return!1;for(let L=0;L<r.length;L++){let B=r.charCodeAt(L);if(B>=7&&B<=13||B===27||B===127)return!1}return!0}function u(r){return c(r)||C(r)}function p(r){return I.IsUndefined(r)||I.IsBigInt(r)}function l(r){return I.IsUndefined(r)||I.IsNumber(r)}function c(r){return I.IsUndefined(r)||I.IsBoolean(r)}function T(r){return I.IsUndefined(r)||I.IsString(r)}function y(r){return I.IsUndefined(r)||I.IsString(r)&&i(r)&&e(r)}function b(r){return I.IsUndefined(r)||I.IsString(r)&&i(r)}function g(r){return I.IsUndefined(r)||C(r)}function m(r){return S(r,"Any")&&T(r.$id)}s.TAny=m;function U(r){return S(r,"Array")&&r.type==="array"&&T(r.$id)&&C(r.items)&&l(r.minItems)&&l(r.maxItems)&&c(r.uniqueItems)&&g(r.contains)&&l(r.minContains)&&l(r.maxContains)}s.TArray=U;function d(r){return S(r,"AsyncIterator")&&r.type==="AsyncIterator"&&T(r.$id)&&C(r.items)}s.TAsyncIterator=d;function O(r){return S(r,"BigInt")&&r.type==="bigint"&&T(r.$id)&&p(r.exclusiveMaximum)&&p(r.exclusiveMinimum)&&p(r.maximum)&&p(r.minimum)&&p(r.multipleOf)}s.TBigInt=O;function v(r){return S(r,"Boolean")&&r.type==="boolean"&&T(r.$id)}s.TBoolean=v;function N(r){return S(r,"Constructor")&&r.type==="Constructor"&&T(r.$id)&&I.IsArray(r.parameters)&&r.parameters.every(L=>C(L))&&C(r.returns)}s.TConstructor=N;function j(r){return S(r,"Date")&&r.type==="Date"&&T(r.$id)&&l(r.exclusiveMaximumTimestamp)&&l(r.exclusiveMinimumTimestamp)&&l(r.maximumTimestamp)&&l(r.minimumTimestamp)&&l(r.multipleOfTimestamp)}s.TDate=j;function R(r){return S(r,"Function")&&r.type==="Function"&&T(r.$id)&&I.IsArray(r.parameters)&&r.parameters.every(L=>C(L))&&C(r.returns)}s.TFunction=R;function A(r){return S(r,"Integer")&&r.type==="integer"&&T(r.$id)&&l(r.exclusiveMaximum)&&l(r.exclusiveMinimum)&&l(r.maximum)&&l(r.minimum)&&l(r.multipleOf)}s.TInteger=A;function K(r){return S(r,"Intersect")&&!(I.IsString(r.type)&&r.type!=="object")&&I.IsArray(r.allOf)&&r.allOf.every(L=>C(L)&&!oe(L))&&T(r.type)&&(c(r.unevaluatedProperties)||g(r.unevaluatedProperties))&&T(r.$id)}s.TIntersect=K;function pe(r){return S(r,"Iterator")&&r.type==="Iterator"&&T(r.$id)&&C(r.items)}s.TIterator=pe;function S(r,L){return ee(r)&&r[o.Kind]===L}s.TKindOf=S;function ee(r){return I.IsObject(r)&&o.Kind in r&&I.IsString(r[o.Kind])}s.TKind=ee;function ne(r){return V(r)&&I.IsString(r.const)}s.TLiteralString=ne;function Te(r){return V(r)&&I.IsNumber(r.const)}s.TLiteralNumber=Te;function Ke(r){return V(r)&&I.IsBoolean(r.const)}s.TLiteralBoolean=Ke;function V(r){return S(r,"Literal")&&T(r.$id)&&(I.IsBoolean(r.const)||I.IsNumber(r.const)||I.IsString(r.const))}s.TLiteral=V;function fe(r){return S(r,"Never")&&I.IsObject(r.not)&&Object.getOwnPropertyNames(r.not).length===0}s.TNever=fe;function $(r){return S(r,"Not")&&C(r.not)}s.TNot=$;function te(r){return S(r,"Null")&&r.type==="null"&&T(r.$id)}s.TNull=te;function re(r){return S(r,"Number")&&r.type==="number"&&T(r.$id)&&l(r.exclusiveMaximum)&&l(r.exclusiveMinimum)&&l(r.maximum)&&l(r.minimum)&&l(r.multipleOf)}s.TNumber=re;function _(r){return S(r,"Object")&&r.type==="object"&&T(r.$id)&&I.IsObject(r.properties)&&u(r.additionalProperties)&&l(r.minProperties)&&l(r.maxProperties)&&Object.entries(r.properties).every(([L,B])=>i(L)&&C(B))}s.TObject=_;function ie(r){return S(r,"Promise")&&r.type==="Promise"&&T(r.$id)&&C(r.item)}s.TPromise=ie;function de(r){return S(r,"Record")&&r.type==="object"&&T(r.$id)&&u(r.additionalProperties)&&I.IsObject(r.patternProperties)&&(L=>{let B=Object.getOwnPropertyNames(L.patternProperties);return B.length===1&&e(B[0])&&I.IsObject(L.patternProperties)&&C(L.patternProperties[B[0]])})(r)}s.TRecord=de;function Ee(r){return I.IsObject(r)&&o.Hint in r&&r[o.Hint]==="Recursive"}s.TRecursive=Ee;function ye(r){return S(r,"Ref")&&T(r.$id)&&I.IsString(r.$ref)}s.TRef=ye;function me(r){return S(r,"String")&&r.type==="string"&&T(r.$id)&&l(r.minLength)&&l(r.maxLength)&&y(r.pattern)&&b(r.format)}s.TString=me;function ge(r){return S(r,"Symbol")&&r.type==="symbol"&&T(r.$id)}s.TSymbol=ge;function z(r){return S(r,"TemplateLiteral")&&r.type==="string"&&I.IsString(r.pattern)&&r.pattern[0]==="^"&&r.pattern[r.pattern.length-1]==="$"}s.TTemplateLiteral=z;function Ie(r){return S(r,"This")&&T(r.$id)&&I.IsString(r.$ref)}s.TThis=Ie;function oe(r){return I.IsObject(r)&&o.Transform in r}s.TTransform=oe;function F(r){return S(r,"Tuple")&&r.type==="array"&&T(r.$id)&&I.IsNumber(r.minItems)&&I.IsNumber(r.maxItems)&&r.minItems===r.maxItems&&(I.IsUndefined(r.items)&&I.IsUndefined(r.additionalItems)&&r.minItems===0||I.IsArray(r.items)&&r.items.every(L=>C(L)))}s.TTuple=F;function be(r){return S(r,"Undefined")&&r.type==="undefined"&&T(r.$id)}s.TUndefined=be;function Be(r){return q(r)&&r.anyOf.every(L=>ne(L)||Te(L))}s.TUnionLiteral=Be;function q(r){return S(r,"Union")&&T(r.$id)&&I.IsObject(r)&&I.IsArray(r.anyOf)&&r.anyOf.every(L=>C(L))}s.TUnion=q;function W(r){return S(r,"Uint8Array")&&r.type==="Uint8Array"&&T(r.$id)&&l(r.minByteLength)&&l(r.maxByteLength)}s.TUint8Array=W;function E(r){return S(r,"Unknown")&&T(r.$id)}s.TUnknown=E;function Oe(r){return S(r,"Unsafe")}s.TUnsafe=Oe;function se(r){return S(r,"Void")&&r.type==="void"&&T(r.$id)}s.TVoid=se;function Me(r){return I.IsObject(r)&&r[o.Readonly]==="Readonly"}s.TReadonly=Me;function De(r){return I.IsObject(r)&&r[o.Optional]==="Optional"}s.TOptional=De;function C(r){return I.IsObject(r)&&(m(r)||U(r)||v(r)||O(r)||d(r)||N(r)||j(r)||R(r)||A(r)||K(r)||pe(r)||V(r)||fe(r)||$(r)||te(r)||re(r)||_(r)||ie(r)||de(r)||ye(r)||me(r)||ge(r)||z(r)||Ie(r)||F(r)||be(r)||q(r)||W(r)||E(r)||Oe(r)||se(r)||ee(r)&&Ve.Has(r[o.Kind]))}s.TSchema=C})(a||(o.TypeGuard=a={}));var Ge;(function(s){function e(i){return i[o.Kind]==="Intersect"?i.allOf.every(u=>e(u)):i[o.Kind]==="Union"?i.anyOf.some(u=>e(u)):i[o.Kind]==="Undefined"?!0:i[o.Kind]==="Not"?!e(i.not):!1}s.Check=e})(Ge||(o.ExtendsUndefined=Ge={}));var Ue=class extends D{};o.TypeExtendsError=Ue;var f;(function(s){s[s.Union=0]="Union",s[s.True=1]="True",s[s.False=2]="False"})(f||(o.TypeExtendsResult=f={}));var J;(function(s){function e(n){return n===f.False?n:f.True}function i(n){throw new Ue(n)}function u(n){return a.TNever(n)||a.TIntersect(n)||a.TUnion(n)||a.TUnknown(n)||a.TAny(n)}function p(n,t){return a.TNever(t)?S(n,t):a.TIntersect(t)?R(n,t):a.TUnion(t)?ke(n,t):a.TUnknown(t)?Qe(n,t):a.TAny(t)?l(n,t):i("StructuralRight")}function l(n,t){return f.True}function c(n,t){return a.TIntersect(t)?R(n,t):a.TUnion(t)&&t.anyOf.some(x=>a.TAny(x)||a.TUnknown(x))?f.True:a.TUnion(t)?f.Union:a.TUnknown(t)||a.TAny(t)?f.True:f.Union}function T(n,t){return a.TUnknown(n)?f.False:a.TAny(n)?f.Union:a.TNever(n)?f.True:f.False}function y(n,t){return a.TObject(t)&&z(t)?f.True:u(t)?p(n,t):a.TArray(t)?e(w(n.items,t.items)):f.False}function b(n,t){return u(t)?p(n,t):a.TAsyncIterator(t)?e(w(n.items,t.items)):f.False}function g(n,t){return u(t)?p(n,t):a.TObject(t)?F(n,t):a.TRecord(t)?E(n,t):a.TBigInt(t)?f.True:f.False}function m(n,t){return a.TLiteral(n)&&I.IsBoolean(n.const)||a.TBoolean(n)?f.True:f.False}function U(n,t){return u(t)?p(n,t):a.TObject(t)?F(n,t):a.TRecord(t)?E(n,t):a.TBoolean(t)?f.True:f.False}function d(n,t){return u(t)?p(n,t):a.TObject(t)?F(n,t):a.TConstructor(t)?n.parameters.length>t.parameters.length?f.False:n.parameters.every((x,M)=>e(w(t.parameters[M],x))===f.True)?e(w(n.returns,t.returns)):f.False:f.False}function O(n,t){return u(t)?p(n,t):a.TObject(t)?F(n,t):a.TRecord(t)?E(n,t):a.TDate(t)?f.True:f.False}function v(n,t){return u(t)?p(n,t):a.TObject(t)?F(n,t):a.TFunction(t)?n.parameters.length>t.parameters.length?f.False:n.parameters.every((x,M)=>e(w(t.parameters[M],x))===f.True)?e(w(n.returns,t.returns)):f.False:f.False}function N(n,t){return a.TLiteral(n)&&I.IsNumber(n.const)||a.TNumber(n)||a.TInteger(n)?f.True:f.False}function j(n,t){return a.TInteger(t)||a.TNumber(t)?f.True:u(t)?p(n,t):a.TObject(t)?F(n,t):a.TRecord(t)?E(n,t):f.False}function R(n,t){return t.allOf.every(x=>w(n,x)===f.True)?f.True:f.False}function A(n,t){return n.allOf.some(x=>w(x,t)===f.True)?f.True:f.False}function K(n,t){return u(t)?p(n,t):a.TIterator(t)?e(w(n.items,t.items)):f.False}function pe(n,t){return a.TLiteral(t)&&t.const===n.const?f.True:u(t)?p(n,t):a.TObject(t)?F(n,t):a.TRecord(t)?E(n,t):a.TString(t)?se(n,t):a.TNumber(t)?V(n,t):a.TInteger(t)?N(n,t):a.TBoolean(t)?m(n,t):f.False}function S(n,t){return f.False}function ee(n,t){return f.True}function ne(n){let[t,x]=[n,0];for(;a.TNot(t);)t=t.not,x+=1;return x%2===0?t:o.Type.Unknown()}function Te(n,t){return a.TNot(n)?w(ne(n),t):a.TNot(t)?w(n,ne(t)):i("Invalid fallthrough for Not")}function Ke(n,t){return u(t)?p(n,t):a.TObject(t)?F(n,t):a.TRecord(t)?E(n,t):a.TNull(t)?f.True:f.False}function V(n,t){return a.TLiteralNumber(n)||a.TNumber(n)||a.TInteger(n)?f.True:f.False}function fe(n,t){return u(t)?p(n,t):a.TObject(t)?F(n,t):a.TRecord(t)?E(n,t):a.TInteger(t)||a.TNumber(t)?f.True:f.False}function $(n,t){return Object.getOwnPropertyNames(n.properties).length===t}function te(n){return z(n)}function re(n){return $(n,0)||$(n,1)&&"description"in n.properties&&a.TUnion(n.properties.description)&&n.properties.description.anyOf.length===2&&(a.TString(n.properties.description.anyOf[0])&&a.TUndefined(n.properties.description.anyOf[1])||a.TString(n.properties.description.anyOf[1])&&a.TUndefined(n.properties.description.anyOf[0]))}function _(n){return $(n,0)}function ie(n){return $(n,0)}function de(n){return $(n,0)}function Ee(n){return $(n,0)}function ye(n){return z(n)}function me(n){let t=o.Type.Number();return $(n,0)||$(n,1)&&"length"in n.properties&&e(w(n.properties.length,t))===f.True}function ge(n){return $(n,0)}function z(n){let t=o.Type.Number();return $(n,0)||$(n,1)&&"length"in n.properties&&e(w(n.properties.length,t))===f.True}function Ie(n){let t=o.Type.Function([o.Type.Any()],o.Type.Any());return $(n,0)||$(n,1)&&"then"in n.properties&&e(w(n.properties.then,t))===f.True}function oe(n,t){return w(n,t)===f.False||a.TOptional(n)&&!a.TOptional(t)?f.False:f.True}function F(n,t){return a.TUnknown(n)?f.False:a.TAny(n)?f.Union:a.TNever(n)||a.TLiteralString(n)&&te(t)||a.TLiteralNumber(n)&&_(t)||a.TLiteralBoolean(n)&&ie(t)||a.TSymbol(n)&&re(t)||a.TBigInt(n)&&de(t)||a.TString(n)&&te(t)||a.TSymbol(n)&&re(t)||a.TNumber(n)&&_(t)||a.TInteger(n)&&_(t)||a.TBoolean(n)&&ie(t)||a.TUint8Array(n)&&ye(t)||a.TDate(n)&&Ee(t)||a.TConstructor(n)&&ge(t)||a.TFunction(n)&&me(t)?f.True:a.TRecord(n)&&a.TString(q(n))?t[o.Hint]==="Record"?f.True:f.False:a.TRecord(n)&&a.TNumber(q(n))?$(t,0)?f.True:f.False:f.False}function be(n,t){return u(t)?p(n,t):a.TRecord(t)?E(n,t):a.TObject(t)?(()=>{for(let x of Object.getOwnPropertyNames(t.properties)){if(!(x in n.properties)&&!a.TOptional(t.properties[x]))return f.False;if(a.TOptional(t.properties[x]))return f.True;if(oe(n.properties[x],t.properties[x])===f.False)return f.False}return f.True})():f.False}function Be(n,t){return u(t)?p(n,t):a.TObject(t)&&Ie(t)?f.True:a.TPromise(t)?e(w(n.item,t.item)):f.False}function q(n){return o.PatternNumberExact in n.patternProperties?o.Type.Number():o.PatternStringExact in n.patternProperties?o.Type.String():i("Unknown record key pattern")}function W(n){return o.PatternNumberExact in n.patternProperties?n.patternProperties[o.PatternNumberExact]:o.PatternStringExact in n.patternProperties?n.patternProperties[o.PatternStringExact]:i("Unable to get record value schema")}function E(n,t){let[x,M]=[q(t),W(t)];return a.TLiteralString(n)&&a.TNumber(x)&&e(w(n,M))===f.True?f.True:a.TUint8Array(n)&&a.TNumber(x)||a.TString(n)&&a.TNumber(x)||a.TArray(n)&&a.TNumber(x)?w(n,M):a.TObject(n)?(()=>{for(let On of Object.getOwnPropertyNames(n.properties))if(oe(M,n.properties[On])===f.False)return f.False;return f.True})():f.False}function Oe(n,t){return u(t)?p(n,t):a.TObject(t)?F(n,t):a.TRecord(t)?w(W(n),W(t)):f.False}function se(n,t){return a.TLiteral(n)&&I.IsString(n.const)||a.TString(n)?f.True:f.False}function Me(n,t){return u(t)?p(n,t):a.TObject(t)?F(n,t):a.TRecord(t)?E(n,t):a.TString(t)?f.True:f.False}function De(n,t){return u(t)?p(n,t):a.TObject(t)?F(n,t):a.TRecord(t)?E(n,t):a.TSymbol(t)?f.True:f.False}function C(n,t){return a.TTemplateLiteral(n)?w(k.Resolve(n),t):a.TTemplateLiteral(t)?w(n,k.Resolve(t)):i("Invalid fallthrough for TemplateLiteral")}function r(n,t){return a.TArray(t)&&n.items!==void 0&&n.items.every(x=>w(x,t.items)===f.True)}function L(n,t){return a.TNever(n)?f.True:a.TUnknown(n)?f.False:a.TAny(n)?f.Union:f.False}function B(n,t){return u(t)?p(n,t):a.TObject(t)&&z(t)||a.TArray(t)&&r(n,t)?f.True:a.TTuple(t)?I.IsUndefined(n.items)&&!I.IsUndefined(t.items)||!I.IsUndefined(n.items)&&I.IsUndefined(t.items)?f.False:I.IsUndefined(n.items)&&!I.IsUndefined(t.items)||n.items.every((x,M)=>w(x,t.items[M])===f.True)?f.True:f.False:f.False}function fn(n,t){return u(t)?p(n,t):a.TObject(t)?F(n,t):a.TRecord(t)?E(n,t):a.TUint8Array(t)?f.True:f.False}function dn(n,t){return u(t)?p(n,t):a.TObject(t)?F(n,t):a.TRecord(t)?E(n,t):a.TVoid(t)?gn(n,t):a.TUndefined(t)?f.True:f.False}function ke(n,t){return t.anyOf.some(x=>w(n,x)===f.True)?f.True:f.False}function yn(n,t){return n.anyOf.every(x=>w(x,t)===f.True)?f.True:f.False}function Qe(n,t){return f.True}function mn(n,t){return a.TNever(t)?S(n,t):a.TIntersect(t)?R(n,t):a.TUnion(t)?ke(n,t):a.TAny(t)?l(n,t):a.TString(t)?se(n,t):a.TNumber(t)?V(n,t):a.TInteger(t)?N(n,t):a.TBoolean(t)?m(n,t):a.TArray(t)?T(n,t):a.TTuple(t)?L(n,t):a.TObject(t)?F(n,t):a.TUnknown(t)?f.True:f.False}function gn(n,t){return a.TUndefined(n)||a.TUndefined(n)?f.True:f.False}function In(n,t){return a.TIntersect(t)?R(n,t):a.TUnion(t)?ke(n,t):a.TUnknown(t)?Qe(n,t):a.TAny(t)?l(n,t):a.TObject(t)?F(n,t):a.TVoid(t)?f.True:f.False}function w(n,t){return a.TTemplateLiteral(n)||a.TTemplateLiteral(t)?C(n,t):a.TNot(n)||a.TNot(t)?Te(n,t):a.TAny(n)?c(n,t):a.TArray(n)?y(n,t):a.TBigInt(n)?g(n,t):a.TBoolean(n)?U(n,t):a.TAsyncIterator(n)?b(n,t):a.TConstructor(n)?d(n,t):a.TDate(n)?O(n,t):a.TFunction(n)?v(n,t):a.TInteger(n)?j(n,t):a.TIntersect(n)?A(n,t):a.TIterator(n)?K(n,t):a.TLiteral(n)?pe(n,t):a.TNever(n)?ee(n,t):a.TNull(n)?Ke(n,t):a.TNumber(n)?fe(n,t):a.TObject(n)?be(n,t):a.TRecord(n)?Oe(n,t):a.TString(n)?Me(n,t):a.TSymbol(n)?De(n,t):a.TTuple(n)?B(n,t):a.TPromise(n)?Be(n,t):a.TUint8Array(n)?fn(n,t):a.TUndefined(n)?dn(n,t):a.TUnion(n)?yn(n,t):a.TUnknown(n)?mn(n,t):a.TVoid(n)?In(n,t):i(`Unknown left type operand '${n[o.Kind]}'`)}function bn(n,t){return w(n,t)}s.Extends=bn})(J||(o.TypeExtends=J={}));var P;(function(s){function e(y){return y.map(b=>l(b))}function i(y){return new Date(y.getTime())}function u(y){return new Uint8Array(y)}function p(y){let b=Object.getOwnPropertyNames(y).reduce((m,U)=>({...m,[U]:l(y[U])}),{}),g=Object.getOwnPropertySymbols(y).reduce((m,U)=>({...m,[U]:l(y[U])}),{});return{...b,...g}}function l(y){return I.IsArray(y)?e(y):I.IsDate(y)?i(y):I.IsUint8Array(y)?u(y):I.IsObject(y)?p(y):y}function c(y){return y.map(b=>T(b))}s.Rest=c;function T(y,b={}){return{...l(y),...b}}s.Type=T})(P||(o.TypeClone=P={}));var qe;(function(s){function e(d){return d.map(O=>{let{[o.Optional]:v,...N}=P.Type(O);return N})}function i(d){return d.every(O=>a.TOptional(O))}function u(d){return d.some(O=>a.TOptional(O))}function p(d){return i(d.allOf)?o.Type.Optional(o.Type.Intersect(e(d.allOf))):d}function l(d){return u(d.anyOf)?o.Type.Optional(o.Type.Union(e(d.anyOf))):d}function c(d){return d[o.Kind]==="Intersect"?p(d):d[o.Kind]==="Union"?l(d):d}function T(d,O){let v=d.allOf.reduce((N,j)=>{let R=m(j,O);return R[o.Kind]==="Never"?N:[...N,R]},[]);return c(o.Type.Intersect(v))}function y(d,O){let v=d.anyOf.map(N=>m(N,O));return c(o.Type.Union(v))}function b(d,O){let v=d.properties[O];return I.IsUndefined(v)?o.Type.Never():o.Type.Union([v])}function g(d,O){let v=d.items;if(I.IsUndefined(v))return o.Type.Never();let N=v[O];return I.IsUndefined(N)?o.Type.Never():N}function m(d,O){return d[o.Kind]==="Intersect"?T(d,O):d[o.Kind]==="Union"?y(d,O):d[o.Kind]==="Object"?b(d,O):d[o.Kind]==="Tuple"?g(d,O):o.Type.Never()}function U(d,O,v={}){let N=O.map(j=>m(d,j.toString()));return c(o.Type.Union(N,v))}s.Resolve=U})(qe||(o.IndexedAccessor=qe={}));var Y;(function(s){function e(g){let[m,U]=[g.slice(0,1),g.slice(1)];return`${m.toLowerCase()}${U}`}function i(g){let[m,U]=[g.slice(0,1),g.slice(1)];return`${m.toUpperCase()}${U}`}function u(g){return g.toUpperCase()}function p(g){return g.toLowerCase()}function l(g,m){let U=X.ParseExact(g.pattern);if(!Z.Check(U))return{...g,pattern:c(g.pattern,m)};let v=[...G.Generate(U)].map(R=>o.Type.Literal(R)),N=T(v,m),j=o.Type.Union(N);return o.Type.TemplateLiteral([j])}function c(g,m){return typeof g=="string"?m==="Uncapitalize"?e(g):m==="Capitalize"?i(g):m==="Uppercase"?u(g):m==="Lowercase"?p(g):g:g.toString()}function T(g,m){if(g.length===0)return[];let[U,...d]=g;return[b(U,m),...T(d,m)]}function y(g,m){return a.TTemplateLiteral(g)?l(g,m):a.TUnion(g)?o.Type.Union(T(g.anyOf,m)):a.TLiteral(g)?o.Type.Literal(c(g.const,m)):g}function b(g,m){return y(g,m)}s.Map=b})(Y||(o.Intrinsic=Y={}));var Q;(function(s){function e(c,T){return o.Type.Intersect(c.allOf.map(y=>p(y,T)),{...c})}function i(c,T){return o.Type.Union(c.anyOf.map(y=>p(y,T)),{...c})}function u(c,T){return T(c)}function p(c,T){return c[o.Kind]==="Intersect"?e(c,T):c[o.Kind]==="Union"?i(c,T):c[o.Kind]==="Object"?u(c,T):c}function l(c,T,y){return{...p(P.Type(c),T),...y}}s.Map=l})(Q||(o.ObjectMap=Q={}));var Pe;(function(s){function e(b){return b[0]==="^"&&b[b.length-1]==="$"?b.slice(1,b.length-1):b}function i(b,g){return b.allOf.reduce((m,U)=>[...m,...c(U,g)],[])}function u(b,g){let m=b.anyOf.map(U=>c(U,g));return[...m.reduce((U,d)=>d.map(O=>m.every(v=>v.includes(O))?U.add(O):U)[0],new Set)]}function p(b,g){return Object.getOwnPropertyNames(b.properties)}function l(b,g){return g.includePatterns?Object.getOwnPropertyNames(b.patternProperties):[]}function c(b,g){return a.TIntersect(b)?i(b,g):a.TUnion(b)?u(b,g):a.TObject(b)?p(b,g):a.TRecord(b)?l(b,g):[]}function T(b,g){return[...new Set(c(b,g))]}s.ResolveKeys=T;function y(b){return`^(${T(b,{includePatterns:!0}).map(U=>`(${e(U)})`).join("|")})$`}s.ResolvePattern=y})(Pe||(o.KeyResolver=Pe={}));var ve=class extends D{};o.KeyArrayResolverError=ve;var ae;(function(s){function e(i){return Array.isArray(i)?i:a.TUnionLiteral(i)?i.anyOf.map(u=>u.const.toString()):a.TLiteral(i)?[i.const]:a.TTemplateLiteral(i)?(()=>{let u=X.ParseExact(i.pattern);if(!Z.Check(u))throw new ve("Cannot resolve keys from infinite template expression");return[...G.Generate(u)]})():[]}s.Resolve=e})(ae||(o.KeyArrayResolver=ae={}));var Je;(function(s){function*e(u){for(let p of u.anyOf)p[o.Kind]==="Union"?yield*e(p):yield p}function i(u){return o.Type.Union([...e(u)],{...u})}s.Resolve=i})(Je||(o.UnionResolver=Je={}));var Ne=class extends D{};o.TemplateLiteralPatternError=Ne;var Se;(function(s){function e(l){throw new Ne(l)}function i(l){return l.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function u(l,c){return a.TTemplateLiteral(l)?l.pattern.slice(1,l.pattern.length-1):a.TUnion(l)?`(${l.anyOf.map(T=>u(T,c)).join("|")})`:a.TNumber(l)?`${c}${o.PatternNumber}`:a.TInteger(l)?`${c}${o.PatternNumber}`:a.TBigInt(l)?`${c}${o.PatternNumber}`:a.TString(l)?`${c}${o.PatternString}`:a.TLiteral(l)?`${c}${i(l.const.toString())}`:a.TBoolean(l)?`${c}${o.PatternBoolean}`:e(`Unexpected Kind '${l[o.Kind]}'`)}function p(l){return`^${l.map(c=>u(c,"")).join("")}$`}s.Create=p})(Se||(o.TemplateLiteralPattern=Se={}));var k;(function(s){function e(i){let u=X.ParseExact(i.pattern);if(!Z.Check(u))return o.Type.String();let p=[...G.Generate(u)].map(l=>o.Type.Literal(l));return o.Type.Union(p)}s.Resolve=e})(k||(o.TemplateLiteralResolver=k={}));var ue=class extends D{};o.TemplateLiteralParserError=ue;var X;(function(s){function e(d,O,v){return d[O]===v&&d.charCodeAt(O-1)!==92}function i(d,O){return e(d,O,"(")}function u(d,O){return e(d,O,")")}function p(d,O){return e(d,O,"|")}function l(d){if(!(i(d,0)&&u(d,d.length-1)))return!1;let O=0;for(let v=0;v<d.length;v++)if(i(d,v)&&(O+=1),u(d,v)&&(O-=1),O===0&&v!==d.length-1)return!1;return!0}function c(d){return d.slice(1,d.length-1)}function T(d){let O=0;for(let v=0;v<d.length;v++)if(i(d,v)&&(O+=1),u(d,v)&&(O-=1),p(d,v)&&O===0)return!0;return!1}function y(d){for(let O=0;O<d.length;O++)if(i(d,O))return!0;return!1}function b(d){let[O,v]=[0,0],N=[];for(let R=0;R<d.length;R++)if(i(d,R)&&(O+=1),u(d,R)&&(O-=1),p(d,R)&&O===0){let A=d.slice(v,R);A.length>0&&N.push(m(A)),v=R+1}let j=d.slice(v);return j.length>0&&N.push(m(j)),N.length===0?{type:"const",const:""}:N.length===1?N[0]:{type:"or",expr:N}}function g(d){function O(j,R){if(!i(j,R))throw new ue("TemplateLiteralParser: Index must point to open parens");let A=0;for(let K=R;K<j.length;K++)if(i(j,K)&&(A+=1),u(j,K)&&(A-=1),A===0)return[R,K];throw new ue("TemplateLiteralParser: Unclosed group parens in expression")}function v(j,R){for(let A=R;A<j.length;A++)if(i(j,A))return[R,A];return[R,j.length]}let N=[];for(let j=0;j<d.length;j++)if(i(d,j)){let[R,A]=O(d,j),K=d.slice(R,A+1);N.push(m(K)),j=A}else{let[R,A]=v(d,j),K=d.slice(R,A);K.length>0&&N.push(m(K)),j=A-1}return N.length===0?{type:"const",const:""}:N.length===1?N[0]:{type:"and",expr:N}}function m(d){return l(d)?m(c(d)):T(d)?b(d):y(d)?g(d):{type:"const",const:d}}s.Parse=m;function U(d){return m(d.slice(1,d.length-1))}s.ParseExact=U})(X||(o.TemplateLiteralParser=X={}));var Re=class extends D{};o.TemplateLiteralFiniteError=Re;var Z;(function(s){function e(c){throw new Re(c)}function i(c){return c.type==="or"&&c.expr.length===2&&c.expr[0].type==="const"&&c.expr[0].const==="0"&&c.expr[1].type==="const"&&c.expr[1].const==="[1-9][0-9]*"}function u(c){return c.type==="or"&&c.expr.length===2&&c.expr[0].type==="const"&&c.expr[0].const==="true"&&c.expr[1].type==="const"&&c.expr[1].const==="false"}function p(c){return c.type==="const"&&c.const===".*"}function l(c){return u(c)?!0:i(c)||p(c)?!1:c.type==="and"?c.expr.every(T=>l(T)):c.type==="or"?c.expr.every(T=>l(T)):c.type==="const"?!0:e("Unknown expression type")}s.Check=l})(Z||(o.TemplateLiteralFinite=Z={}));var xe=class extends D{};o.TemplateLiteralGeneratorError=xe;var G;(function(s){function*e(c){if(c.length===1)return yield*c[0];for(let T of c[0])for(let y of e(c.slice(1)))yield`${T}${y}`}function*i(c){return yield*e(c.expr.map(T=>[...l(T)]))}function*u(c){for(let T of c.expr)yield*l(T)}function*p(c){return yield c.const}function*l(c){return c.type==="and"?yield*i(c):c.type==="or"?yield*u(c):c.type==="const"?yield*p(c):(()=>{throw new xe("Unknown expression")})()}s.Generate=l})(G||(o.TemplateLiteralGenerator=G={}));var He;(function(s){function*e(l){let c=l.trim().replace(/"|'/g,"");return c==="boolean"?yield o.Type.Boolean():c==="number"?yield o.Type.Number():c==="bigint"?yield o.Type.BigInt():c==="string"?yield o.Type.String():yield(()=>{let T=c.split("|").map(y=>o.Type.Literal(y.trim()));return T.length===0?o.Type.Never():T.length===1?T[0]:o.Type.Union(T)})()}function*i(l){if(l[1]!=="{"){let c=o.Type.Literal("$"),T=u(l.slice(1));return yield*[c,...T]}for(let c=2;c<l.length;c++)if(l[c]==="}"){let T=e(l.slice(2,c)),y=u(l.slice(c+1));return yield*[...T,...y]}yield o.Type.Literal(l)}function*u(l){for(let c=0;c<l.length;c++)if(l[c]==="$"){let T=o.Type.Literal(l.slice(0,c)),y=i(l.slice(c));return yield*[T,...y]}yield o.Type.Literal(l)}function p(l){return[...u(l)]}s.Parse=p})(He||(o.TemplateLiteralDslParser=He={}));var je=class{constructor(e){this.schema=e}Decode(e){return new we(this.schema,e)}};o.TransformDecodeBuilder=je;var we=class{constructor(e,i){this.schema=e,this.decode=i}Encode(e){let i=P.Type(this.schema);return a.TTransform(i)?(()=>{let l={Encode:c=>i[o.Transform].Encode(e(c)),Decode:c=>this.decode(i[o.Transform].Decode(c))};return{...i,[o.Transform]:l}})():(()=>{let u={Decode:this.decode,Encode:e};return{...i,[o.Transform]:u}})()}};o.TransformEncodeBuilder=we;var wn=0,Le=class extends D{};o.TypeBuilderError=Le;var Ae=class{Create(e){return e}Throw(e){throw new Le(e)}Discard(e,i){return i.reduce((u,p)=>{let{[p]:l,...c}=u;return c},e)}Strict(e){return JSON.parse(JSON.stringify(e))}};o.TypeBuilder=Ae;var le=class extends Ae{ReadonlyOptional(e){return this.Readonly(this.Optional(e))}Readonly(e){return{...P.Type(e),[o.Readonly]:"Readonly"}}Optional(e){return{...P.Type(e),[o.Optional]:"Optional"}}Any(e={}){return this.Create({...e,[o.Kind]:"Any"})}Array(e,i={}){return this.Create({...i,[o.Kind]:"Array",type:"array",items:P.Type(e)})}Boolean(e={}){return this.Create({...e,[o.Kind]:"Boolean",type:"boolean"})}Capitalize(e,i={}){return{...Y.Map(P.Type(e),"Capitalize"),...i}}Composite(e,i){let u=o.Type.Intersect(e,{}),l=Pe.ResolveKeys(u,{includePatterns:!1}).reduce((c,T)=>({...c,[T]:o.Type.Index(u,[T])}),{});return o.Type.Object(l,i)}Enum(e,i={}){if(I.IsUndefined(e))return this.Throw("Enum undefined or empty");let u=Object.getOwnPropertyNames(e).filter(c=>isNaN(c)).map(c=>e[c]),l=[...new Set(u)].map(c=>o.Type.Literal(c));return this.Union(l,{...i,[o.Hint]:"Enum"})}Extends(e,i,u,p,l={}){switch(J.Extends(e,i)){case f.Union:return this.Union([P.Type(u,l),P.Type(p,l)]);case f.True:return P.Type(u,l);case f.False:return P.Type(p,l)}}Exclude(e,i,u={}){return a.TTemplateLiteral(e)?this.Exclude(k.Resolve(e),i,u):a.TTemplateLiteral(i)?this.Exclude(e,k.Resolve(i),u):a.TUnion(e)?(()=>{let p=e.anyOf.filter(l=>J.Extends(l,i)===f.False);return p.length===1?P.Type(p[0],u):this.Union(p,u)})():J.Extends(e,i)!==f.False?this.Never(u):P.Type(e,u)}Extract(e,i,u={}){return a.TTemplateLiteral(e)?this.Extract(k.Resolve(e),i,u):a.TTemplateLiteral(i)?this.Extract(e,k.Resolve(i),u):a.TUnion(e)?(()=>{let p=e.anyOf.filter(l=>J.Extends(l,i)!==f.False);return p.length===1?P.Type(p[0],u):this.Union(p,u)})():J.Extends(e,i)!==f.False?P.Type(e,u):this.Never(u)}Index(e,i,u={}){return a.TArray(e)&&a.TNumber(i)?P.Type(e.items,u):a.TTuple(e)&&a.TNumber(i)?(()=>{let l=(I.IsUndefined(e.items)?[]:e.items).map(c=>P.Type(c));return this.Union(l,u)})():(()=>{let p=ae.Resolve(i),l=P.Type(e);return qe.Resolve(l,p,u)})()}Integer(e={}){return this.Create({...e,[o.Kind]:"Integer",type:"integer"})}Intersect(e,i={}){if(e.length===0)return o.Type.Never();if(e.length===1)return P.Type(e[0],i);e.some(c=>a.TTransform(c))&&this.Throw("Cannot intersect transform types");let u=e.every(c=>a.TObject(c)),p=P.Rest(e),l=a.TSchema(i.unevaluatedProperties)?{unevaluatedProperties:P.Type(i.unevaluatedProperties)}:{};return i.unevaluatedProperties===!1||a.TSchema(i.unevaluatedProperties)||u?this.Create({...i,...l,[o.Kind]:"Intersect",type:"object",allOf:p}):this.Create({...i,...l,[o.Kind]:"Intersect",allOf:p})}KeyOf(e,i={}){return a.TRecord(e)?(()=>{let u=Object.getOwnPropertyNames(e.patternProperties)[0];return u===o.PatternNumberExact?this.Number(i):u===o.PatternStringExact?this.String(i):this.Throw("Unable to resolve key type from Record key pattern")})():a.TTuple(e)?(()=>{let p=(I.IsUndefined(e.items)?[]:e.items).map((l,c)=>o.Type.Literal(c.toString()));return this.Union(p,i)})():a.TArray(e)?this.Number(i):(()=>{let u=Pe.ResolveKeys(e,{includePatterns:!1});if(u.length===0)return this.Never(i);let p=u.map(l=>this.Literal(l));return this.Union(p,i)})()}Literal(e,i={}){return this.Create({...i,[o.Kind]:"Literal",const:e,type:typeof e})}Lowercase(e,i={}){return{...Y.Map(P.Type(e),"Lowercase"),...i}}Never(e={}){return this.Create({...e,[o.Kind]:"Never",not:{}})}Not(e,i){return this.Create({...i,[o.Kind]:"Not",not:P.Type(e)})}Null(e={}){return this.Create({...e,[o.Kind]:"Null",type:"null"})}Number(e={}){return this.Create({...e,[o.Kind]:"Number",type:"number"})}Object(e,i={}){let u=Object.getOwnPropertyNames(e),p=u.filter(y=>a.TOptional(e[y])),l=u.filter(y=>!p.includes(y)),c=a.TSchema(i.additionalProperties)?{additionalProperties:P.Type(i.additionalProperties)}:{},T=u.reduce((y,b)=>({...y,[b]:P.Type(e[b])}),{});return l.length>0?this.Create({...i,...c,[o.Kind]:"Object",type:"object",properties:T,required:l}):this.Create({...i,...c,[o.Kind]:"Object",type:"object",properties:T})}Omit(e,i,u={}){let p=ae.Resolve(i);return Q.Map(this.Discard(P.Type(e),["$id",o.Transform]),l=>{I.IsArray(l.required)&&(l.required=l.required.filter(c=>!p.includes(c)),l.required.length===0&&delete l.required);for(let c of Object.getOwnPropertyNames(l.properties))p.includes(c)&&delete l.properties[c];return this.Create(l)},u)}Partial(e,i={}){return Q.Map(this.Discard(P.Type(e),["$id",o.Transform]),u=>{let p=Object.getOwnPropertyNames(u.properties).reduce((l,c)=>({...l,[c]:this.Optional(u.properties[c])}),{});return this.Object(p,this.Discard(u,["required"]))},i)}Pick(e,i,u={}){let p=ae.Resolve(i);return Q.Map(this.Discard(P.Type(e),["$id",o.Transform]),l=>{I.IsArray(l.required)&&(l.required=l.required.filter(c=>p.includes(c)),l.required.length===0&&delete l.required);for(let c of Object.getOwnPropertyNames(l.properties))p.includes(c)||delete l.properties[c];return this.Create(l)},u)}Record(e,i,u={}){return a.TTemplateLiteral(e)?(()=>{let p=X.ParseExact(e.pattern);return Z.Check(p)?this.Object([...G.Generate(p)].reduce((l,c)=>({...l,[c]:P.Type(i)}),{}),u):this.Create({...u,[o.Kind]:"Record",type:"object",patternProperties:{[e.pattern]:P.Type(i)}})})():a.TUnion(e)?(()=>{let p=Je.Resolve(e);if(a.TUnionLiteral(p)){let l=p.anyOf.reduce((c,T)=>({...c,[T.const]:P.Type(i)}),{});return this.Object(l,{...u,[o.Hint]:"Record"})}else this.Throw("Record key of type union contains non-literal types")})():a.TLiteral(e)?I.IsString(e.const)||I.IsNumber(e.const)?this.Object({[e.const]:P.Type(i)},u):this.Throw("Record key of type literal is not of type string or number"):a.TInteger(e)||a.TNumber(e)?this.Create({...u,[o.Kind]:"Record",type:"object",patternProperties:{[o.PatternNumberExact]:P.Type(i)}}):a.TString(e)?(()=>{let p=I.IsUndefined(e.pattern)?o.PatternStringExact:e.pattern;return this.Create({...u,[o.Kind]:"Record",type:"object",patternProperties:{[p]:P.Type(i)}})})():this.Never()}Recursive(e,i={}){I.IsUndefined(i.$id)&&(i.$id=`T${wn++}`);let u=e({[o.Kind]:"This",$ref:`${i.$id}`});return u.$id=i.$id,this.Create({...i,[o.Hint]:"Recursive",...u})}Ref(e,i={}){return I.IsString(e)?this.Create({...i,[o.Kind]:"Ref",$ref:e}):(I.IsUndefined(e.$id)&&this.Throw("Reference target type must specify an $id"),this.Create({...i,[o.Kind]:"Ref",$ref:e.$id}))}Required(e,i={}){return Q.Map(this.Discard(P.Type(e),["$id",o.Transform]),u=>{let p=Object.getOwnPropertyNames(u.properties).reduce((l,c)=>({...l,[c]:this.Discard(u.properties[c],[o.Optional])}),{});return this.Object(p,u)},i)}Rest(e){return a.TTuple(e)&&!I.IsUndefined(e.items)?P.Rest(e.items):a.TIntersect(e)?P.Rest(e.allOf):a.TUnion(e)?P.Rest(e.anyOf):[]}String(e={}){return this.Create({...e,[o.Kind]:"String",type:"string"})}TemplateLiteral(e,i={}){let u=I.IsString(e)?Se.Create(He.Parse(e)):Se.Create(e);return this.Create({...i,[o.Kind]:"TemplateLiteral",type:"string",pattern:u})}Transform(e){return new je(e)}Tuple(e,i={}){let[u,p,l]=[!1,e.length,e.length],c=P.Rest(e),T=e.length>0?{...i,[o.Kind]:"Tuple",type:"array",items:c,additionalItems:u,minItems:p,maxItems:l}:{...i,[o.Kind]:"Tuple",type:"array",minItems:p,maxItems:l};return this.Create(T)}Uncapitalize(e,i={}){return{...Y.Map(P.Type(e),"Uncapitalize"),...i}}Union(e,i={}){return a.TTemplateLiteral(e)?k.Resolve(e):(()=>{let u=e;if(u.length===0)return this.Never(i);if(u.length===1)return this.Create(P.Type(u[0],i));let p=P.Rest(u);return this.Create({...i,[o.Kind]:"Union",anyOf:p})})()}Unknown(e={}){return this.Create({...e,[o.Kind]:"Unknown"})}Unsafe(e={}){return this.Create({...e,[o.Kind]:e[o.Kind]||"Unsafe"})}Uppercase(e,i={}){return{...Y.Map(P.Type(e),"Uppercase"),...i}}};o.JsonTypeBuilder=le;var Fe=class extends le{AsyncIterator(e,i={}){return this.Create({...i,[o.Kind]:"AsyncIterator",type:"AsyncIterator",items:P.Type(e)})}Awaited(e,i={}){let u=p=>p.length>0?(()=>{let[l,...c]=p;return[this.Awaited(l),...u(c)]})():p;return a.TIntersect(e)?o.Type.Intersect(u(e.allOf)):a.TUnion(e)?o.Type.Union(u(e.anyOf)):a.TPromise(e)?this.Awaited(e.item):P.Type(e,i)}BigInt(e={}){return this.Create({...e,[o.Kind]:"BigInt",type:"bigint"})}ConstructorParameters(e,i={}){return this.Tuple([...e.parameters],{...i})}Constructor(e,i,u){let[p,l]=[P.Rest(e),P.Type(i)];return this.Create({...u,[o.Kind]:"Constructor",type:"Constructor",parameters:p,returns:l})}Date(e={}){return this.Create({...e,[o.Kind]:"Date",type:"Date"})}Function(e,i,u){let[p,l]=[P.Rest(e),P.Type(i)];return this.Create({...u,[o.Kind]:"Function",type:"Function",parameters:p,returns:l})}InstanceType(e,i={}){return P.Type(e.returns,i)}Iterator(e,i={}){return this.Create({...i,[o.Kind]:"Iterator",type:"Iterator",items:P.Type(e)})}Parameters(e,i={}){return this.Tuple(e.parameters,{...i})}Promise(e,i={}){return this.Create({...i,[o.Kind]:"Promise",type:"Promise",item:P.Type(e)})}RegExp(e,i={}){let u=I.IsString(e)?e:e.source;return this.Create({...i,[o.Kind]:"String",type:"string",pattern:u})}RegEx(e,i={}){return this.RegExp(e,i)}ReturnType(e,i={}){return P.Type(e.returns,i)}Symbol(e){return this.Create({...e,[o.Kind]:"Symbol",type:"symbol"})}Undefined(e={}){return this.Create({...e,[o.Kind]:"Undefined",type:"undefined"})}Uint8Array(e={}){return this.Create({...e,[o.Kind]:"Uint8Array",type:"Uint8Array"})}Void(e={}){return this.Create({...e,[o.Kind]:"Void",type:"void"})}};o.JavaScriptTypeBuilder=Fe;o.JsonType=new le;o.Type=new Fe});var ce=jn(he(),1),en=ce.Type.String({pattern:".*\\{languageTag|locale\\}.*\\.json$",examples:["./messages/{locale}.json","./i18n/{locale}.json"],title:"Path to language files",description:"Specify the pathPattern to locate resource files in your repository. It must include `{locale}` and end with `.json`."}),Ln=ce.Type.Array(en,{title:"Paths to language files",description:"Specify multiple pathPatterns to locate resource files in your repository. Each must include `{locale}` and end with `.json`."}),Ce=ce.Type.Object({pathPattern:ce.Type.Union([en,Ln])});var nn=s=>s.map(e=>{switch(e.type){case"Text":return e.value;case"VariableReference":return`{${e.name}}`}}).join("");var tn=s=>{let e={};for(let i of s.variants){if(e[i.languageTag]!==void 0)throw new Error(`The message "${s.id}" has multiple variants for the language tag "${i.languageTag}". The inlang-message-format plugin does not support multiple variants for the same language tag at the moment.`);e[i.languageTag]=nn(i.pattern)}return e};var rn=s=>{let e=/\{([^}]+)\}/g,i,u=0,p=[];for(;(i=e.exec(s))!==null;){let c=i[1],T=s.slice(u,i.index);T.length>0&&p.push({type:"Text",value:T}),p.push({type:"VariableReference",name:c}),u=i.index+i[0].length}let l=s.slice(Math.max(0,u));return l.length>0&&p.push({type:"Text",value:l}),p};var _e=s=>({id:s.key,alias:{},selectors:[],variants:[{languageTag:s.languageTag,match:[],pattern:rn(s.value)}]});var An="plugin.inlang.messageFormat",H={id:An,displayName:"Inlang Message Format",description:"A plugin for the inlang SDK that uses a JSON file per language tag to store translations.",key:"inlang-message-format",settingsSchema:Ce,loadMessages:async({settings:s,nodeishFs:e})=>{await $n({settings:s,nodeishFs:e});let i={};for(let u of s.languageTags)try{let p=await e.readFile(s["plugin.inlang.messageFormat"].pathPattern.replace("{languageTag}",u),{encoding:"utf-8"}),l=JSON.parse(p);for(let c in l)c!=="$schema"&&(i[c]?i[c].variants=[...i[c].variants,..._e({key:c,value:l[c],languageTag:u}).variants]:i[c]=_e({key:c,value:l[c],languageTag:u}))}catch(p){if(p?.code!=="ENOENT")throw p}return Object.values(i)},saveMessages:async({settings:s,nodeishFs:e,messages:i})=>{let u={};for(let p of i){let l=tn(p);for(let[c,T]of Object.entries(l))u[c]===void 0&&(u[c]={}),u[c][p.id]=T}for(let[p,l]of Object.entries(u)){let c=s["plugin.inlang.messageFormat"].pathPattern.replace("{languageTag}",p);await Fn({path:c,nodeishFs:e}),await e.writeFile(s["plugin.inlang.messageFormat"].pathPattern.replace("{languageTag}",p),(T=>JSON.stringify(T,void 0,"	"))({$schema:"https://inlang.com/schema/inlang-message-format",...l}))}}},Fn=async s=>{try{await s.nodeishFs.mkdir(Cn(s.path),{recursive:!0})}catch{}};function Cn(s){if(s.length===0)return".";let e=s.charCodeAt(0),i=e===47,u=-1,p=!0;for(let l=s.length-1;l>=1;--l)if(e=s.charCodeAt(l),e===47){if(!p){u=l;break}}else p=!1;return u===-1?i?"/":".":i&&u===1?"//":s.slice(0,u)}var $n=async s=>{if(s.settings["plugin.inlang.messageFormat"].filePath!=null)try{let e=await s.nodeishFs.readFile(s.settings["plugin.inlang.messageFormat"].filePath,{encoding:"utf-8"});await H.saveMessages?.({messages:JSON.parse(e).data,nodeishFs:s.nodeishFs,settings:s.settings}),console.log("Migration to v2 of the inlang-message-format plugin was successful. Please delete the old messages.json file and the filePath property in the settings file of the project.")}catch{}};var on=async({settings:s})=>{let e=[],i=s[h]?.pathPattern?Array.isArray(s[h].pathPattern)?s[h].pathPattern:[s[h].pathPattern]:[];for(let u of i)for(let p of s.locales)e.push({locale:p,path:u.replace(/{(locale|languageTag)}/,p)});return e};function sn(s){return s&&s.constructor&&typeof s.constructor.isBuffer=="function"&&s.constructor.isBuffer(s)}function an(s){return s}function We(s,e){e=e||{};let i=e.delimiter||".",u=e.maxDepth,p=e.transformKey||an,l={};function c(T,y,b){b=b||1,Object.keys(T).forEach(function(g){let m=T[g],U=e.safe&&Array.isArray(m),d=Object.prototype.toString.call(m),O=sn(m),v=d==="[object Object]"||d==="[object Array]",N=y?y+i+p(g):p(g);if(!U&&!O&&v&&Object.keys(m).length&&(!e.maxDepth||b<u))return c(m,N,b+1);l[N]=m})}return c(s),l}function Ye(s,e){e=e||{};let i=e.delimiter||".",u=e.overwrite||!1,p=e.transformKey||an,l={};if(sn(s)||Object.prototype.toString.call(s)!=="[object Object]")return s;function T(g){let m=Number(g);return isNaN(m)||g.indexOf(".")!==-1||e.object?g:m}function y(g,m,U){return Object.keys(U).reduce(function(d,O){return d[g+i+O]=U[O],d},m)}function b(g){let m=Object.prototype.toString.call(g),U=m==="[object Array]",d=m==="[object Object]";if(g){if(U)return!g.length;if(d)return!Object.keys(g).length}else return!0}return s=Object.keys(s).reduce(function(g,m){let U=Object.prototype.toString.call(s[m]);return!(U==="[object Object]"||U==="[object Array]")||b(s[m])?(g[m]=s[m],g):y(m,g,We(s[m],e))},{}),Object.keys(s).forEach(function(g){let m=g.split(i).map(p),U=T(m.shift()),d=T(m[0]),O=l;for(;d!==void 0;){if(U==="__proto__")return;let v=Object.prototype.toString.call(O[U]),N=v==="[object Object]"||v==="[object Array]";if(!u&&!N&&typeof O[U]<"u")return;(u&&!N||!u&&O[U]==null)&&(O[U]=typeof d=="number"&&!e.object?[]:{}),O=O[U],m.length>0&&(U=T(m.shift()),d=T(m[0]))}O[U]=Ye(s[g],e)}),l}var ln=async({files:s})=>{let e=[],i=[],u=[];for(let p of s){let l=JSON.parse(new TextDecoder().decode(p.content)),c=We(l,{safe:!0});for(let T in c){if(T==="$schema")continue;let y=Kn(T,p.locale,c[T]);i.push(y.message),u.push(...y.variants);let b=e.find(g=>g.id===y.bundle.id);b===void 0?e.push(y.bundle):b.declarations=$e([...b.declarations,...y.bundle.declarations])}}return{bundles:e,messages:i,variants:u}};function Kn(s,e,i){let u=En(s,e,i),p=$e(u.declarations),l=$e(u.selectors),c=l.filter(T=>p.find(y=>y.name===T.name)===void 0);for(let T of c)p.push({type:"input-variable",name:T.name});return{bundle:{id:s,declarations:p},message:{bundleId:s,selectors:l,locale:e},variants:u.variants}}function En(s,e,i){if(typeof i=="string"){let y=un(i);return{variants:[{messageBundleId:s,messageLocale:e,matches:[],pattern:y.pattern}],declarations:y.declarations,selectors:[]}}let u=i[0],p=[],l=(u.selectors??[]).map(y=>({type:"variable-reference",name:y})),c=new Set;for(let y of u.declarations??[])c.add(Mn(y));let T=new Set;for(let[y,b]of Object.entries(u.match)){let g=un(b),m=Bn(y);for(let U of g.declarations){let d=!1;for(let O of c)if(O.name===U.name){d=!0;break}if(d)break;c.add(U)}for(let U of m.selectors)T.add(U);p.push({messageBundleId:s,messageLocale:e,matches:m.matches,pattern:g.pattern})}return{variants:p,declarations:Array.from(c),selectors:$e([...l,...Array.from(T)])}}function un(s){let e=[],i=[],u=s.split(/(\{.*?\})/).filter(p=>p!=="");for(let p of u)if((p.startsWith("{")&&p.endsWith("}"))===!1)e.push({type:"text",value:p});else{let l=p.slice(1,-1);i.push({type:"input-variable",name:l}),e.push({type:"expression",arg:{type:"variable-reference",name:l}})}return{declarations:i,pattern:e}}function Bn(s){let e=s.replace(" ",""),i=[],u=[],p=e.split(",");for(let l of p){let[c,T]=l.split("=");!c||!T||(T==="*"?i.push({type:"catchall-match",key:c}):i.push({type:"literal-match",key:c,value:T}),u.push({type:"variable-reference",name:c}))}return{matches:i,selectors:u}}var $e=s=>[...new Set(s.map(e=>JSON.stringify(e)))].map(e=>JSON.parse(e));function Mn(s){if(s.startsWith("input"))return{type:"input-variable",name:s.slice(6).trim()};if(s.startsWith("local")){let e=s.match(/local (\w+) = (\w+): (\w+)(.*)/),[,i,u,p,l]=e,c=l?.trim().split(/\s+/).map(T=>{let[y,b]=T.split("=");return y&&b?{name:y,value:{type:"literal",value:b}}:null}).filter(Boolean);return{type:"local-variable",name:i.trim(),value:{type:"expression",arg:{type:"variable-reference",name:u.trim()},annotation:p?{type:"function-reference",name:p.trim(),options:c??[]}:void 0}}}throw new Error("Unsupported declaration type")}var pn=async({bundles:s,messages:e,variants:i})=>{let u={};for(let l of e){let c=s.find(y=>y.id===l.bundleId),T=[...i.reduce((y,b)=>(b.messageId===l.id&&y.set(JSON.stringify(b.matches),b),y),new Map).values()];u[l.locale]={...u[l.locale],...Dn(c,l,T)}}let p=[];for(let l in u)p.push({locale:l,content:new TextEncoder().encode(JSON.stringify(Ye({$schema:"https://inlang.com/schema/inlang-message-format",...u[l]}),void 0,"	")),name:l+".json"});return p};function Dn(s,e,i){let u=e.bundleId,p=kn(s,e,i);return{[u]:p}}function kn(s,e,i){if(i.length===1&&e.selectors.length===0&&s.declarations.some(p=>p.type!=="input-variable")===!1)return cn(i[0].pattern);let u=[];for(let p of i){if(p.matches.length===0)for(let T of p.pattern)T.type==="expression"&&T.arg.type==="variable-reference"&&p.matches.push({key:T.arg.name,type:"catchall-match"});let l=cn(p.pattern),c=Vn(p.matches);u.push([c,l])}return[{declarations:s.declarations.sort((p,l)=>p.name.localeCompare(l.name)).map(zn).sort(),selectors:e.selectors.map(p=>p.name).sort(),match:Object.fromEntries(u)}]}function cn(s){let e="";for(let i of s)if(i.type==="text")e+=i.value;else if(i.arg.type==="variable-reference")e+=`{${i.arg.name}}`;else throw new Error("Unsupported expression type");return e}function Vn(s){return s.sort((i,u)=>i.key.localeCompare(u.key)).map(i=>i.type==="literal-match"?`${i.key}=${i.value}`:`${i.key}=*`).join(", ")}function zn(s){if(s.type==="input-variable")return`input ${s.name}`;if(s.type==="local-variable"){let e="";if(s.value.arg.type==="variable-reference"?e=`local ${s.name} = ${s.value.arg.name}`:s.value.arg.type==="literal"&&(e=`local ${s.name} = "${s.value.arg.value}"`),s.value.annotation&&(e+=`: ${s.value.annotation.name}`),s.value.annotation?.options)for(let i of s.value?.annotation?.options??[]){if(i.value.type!=="literal")throw new Error("Unsupported option type");e+=` ${i.name}=${i.value.value}`}return e}throw new Error("Unsupported declaration type")}var h="plugin.inlang.messageFormat",Tn={key:h,id:H.id,displayName:H.displayName,description:H.description,loadMessages:H.loadMessages,saveMessages:H.saveMessages,settingsSchema:Ce,toBeImportedFiles:on,importFiles:ln,exportFiles:pn};var It=Tn;export{It as default};
