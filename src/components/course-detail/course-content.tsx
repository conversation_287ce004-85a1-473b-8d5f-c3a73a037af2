import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Eye,
  FileText,
  Grid3X3,
  List,
  Plus,
  Trophy,
  Video,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import type { Lesson } from "./types";

interface CourseContentProps {
  lessons: Lesson[];
}

export const CourseContent = ({ lessons }: CourseContentProps) => {
  const [viewMode, setViewMode] = useState<"list" | "cards">("cards");

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case "video":
        return Video;
      case "text":
        return FileText;
      case "quiz":
        return Trophy;
      default:
        return BookOpen;
    }
  };

  const getContentTypeColor = (type: string) => {
    switch (type) {
      case "video":
        return "bg-red-500";
      case "text":
        return "bg-blue-500";
      case "quiz":
        return "bg-green-500";
      case "game":
        return "bg-purple-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusColor = (status: string) => {
    return status === "Đã xuất bản"
      ? "bg-green-100 text-green-800 border-green-200"
      : "bg-yellow-100 text-yellow-800 border-yellow-200";
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="border-gray-100 border-b">
        <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
          <div>
            <CardTitle className="font-semibold text-lg">
              Nội dung khóa học
            </CardTitle>
            <CardDescription className="mt-1">
              Quản lý các bài học và tài liệu trong khóa học
            </CardDescription>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1 rounded-lg bg-gray-100 p-1">
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="h-8 px-3 shadow-none"
              >
                <List className="mr-1 h-4 w-4" />
                Danh sách
              </Button>
              <Button
                variant={viewMode === "cards" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("cards")}
                className="h-8 px-3 shadow-none"
              >
                <Grid3X3 className="mr-1 h-4 w-4" />
                Thẻ
              </Button>
            </div>
            <Button className="bg-blue-600 shadow-sm hover:bg-blue-700">
              <Plus className="mr-2 h-4 w-4" />
              Thêm nội dung mới
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        {viewMode === "list" ? (
          <div className="space-y-3">
            {lessons.map((lesson) => {
              const Icon = getContentTypeIcon(lesson.contentType);
              return (
                <div
                  key={lesson.id}
                  className="group flex items-center justify-between rounded-xl border border-gray-200 p-4 transition-all duration-200 hover:border-gray-300 hover:bg-gray-50"
                >
                  <div className="flex items-center gap-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 transition-shadow group-hover:shadow-md">
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 transition-colors group-hover:text-blue-600">
                        {lesson.title}
                      </h3>
                      <div className="mt-1 flex items-center gap-4 text-gray-600 text-sm">
                        <span className="flex items-center gap-1">
                          <div
                            className={`h-2 w-2 rounded-full ${getContentTypeColor(
                              lesson.contentType,
                            )}`}
                          />
                          {lesson.type}
                        </span>
                        <span>•</span>
                        <span>{lesson.duration}</span>
                        <span>•</span>
                        <span>Module {lesson.module}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge
                      className={`${getStatusColor(lesson.status)} shadow-sm`}
                      variant="outline"
                    >
                      {lesson.status}
                    </Badge>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" className="shadow-sm">
                        <Edit className="mr-1 h-4 w-4" />
                        Chỉnh sửa
                      </Button>
                      <Button variant="outline" size="sm" className="shadow-sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {lessons.map((lesson) => {
              const Icon = getContentTypeIcon(lesson.contentType);
              return (
                <Card
                  key={lesson.id}
                  className="group border-gray-200 transition-all duration-200 hover:border-gray-300 hover:shadow-lg"
                >
                  <CardHeader className="pb-3">
                    <div className="mb-3 flex items-center justify-between">
                      <Badge variant="outline" className="text-xs shadow-sm">
                        Module {lesson.module}
                      </Badge>
                      <Badge
                        className={`${getStatusColor(
                          lesson.status,
                        )} text-xs shadow-sm`}
                        variant="outline"
                      >
                        {lesson.status}
                      </Badge>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 transition-shadow group-hover:shadow-md">
                        <Icon className="h-5 w-5 text-white" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <CardTitle className="line-clamp-2 text-base leading-snug transition-colors group-hover:text-blue-600">
                          {lesson.title}
                        </CardTitle>
                        <p className="mt-1 truncate font-mono text-gray-500 text-xs">
                          {lesson.slug}
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Sections:</span>
                        <span className="font-semibold text-gray-900">
                          {lesson.sections} phần
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div
                          className={`h-3 w-3 rounded-full ${getContentTypeColor(
                            lesson.contentType,
                          )}`}
                        />
                        <span className="text-gray-600 text-sm capitalize">
                          {lesson.contentType} • {lesson.duration}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1 shadow-sm transition-colors hover:border-blue-200 hover:bg-blue-50 hover:text-blue-700"
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Chỉnh sửa
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="shadow-sm transition-colors hover:bg-gray-50"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
