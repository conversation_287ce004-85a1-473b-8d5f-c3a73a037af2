import { Plus, Search, Users } from "lucide-react";
import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import type { Department, Permission, User } from "./types";

interface CoursePermissionsProps {
  departmentPermissions: Permission[];
  individualPermissions: Permission[];
  departments: Department[];
  users: User[];
}

export function CoursePermissions({
  departmentPermissions,
  individualPermissions,
  departments,
  users,
}: CoursePermissionsProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [permissionMode, setPermissionMode] = useState<
    "department" | "individual"
  >("department");
  const [userSearchTerm, setUserSearchTerm] = useState("");
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [selectedDepartments, setSelectedDepartments] = useState<number[]>([]);

  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(userSearchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(userSearchTerm.toLowerCase()),
  );

  const handleSavePermissions = () => {
    setIsDialogOpen(false);
    setSelectedUsers([]);
    setSelectedDepartments([]);
    setUserSearchTerm("");
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="border-gray-100 border-b">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="font-semibold text-lg">
              Quản lý phân quyền truy cập
            </CardTitle>
            <CardDescription className="mt-1">
              Phân quyền truy cập khóa học theo phòng ban hoặc người dùng cụ thể
            </CardDescription>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 shadow-sm hover:bg-blue-700">
                <Plus className="mr-2 h-4 w-4" />
                Thêm quyền truy cập
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Phân quyền truy cập khóa học</DialogTitle>
                <DialogDescription>
                  Chọn cách thức phân quyền cho khóa học này
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-6">
                <div>
                  <Label className="font-medium text-base">
                    Chọn phương thức phân quyền
                  </Label>
                  <div className="mt-3 grid grid-cols-2 gap-4">
                    <Button
                      variant={
                        permissionMode === "department" ? "default" : "outline"
                      }
                      className="h-auto flex-col gap-2 p-4 shadow-sm"
                      onClick={() => setPermissionMode("department")}
                    >
                      <Users className="h-6 w-6" />
                      <div className="text-center">
                        <div className="font-medium">Theo phòng ban</div>
                        <div className="text-muted-foreground text-sm">
                          Phân quyền cho toàn bộ phòng ban
                        </div>
                      </div>
                    </Button>
                    <Button
                      variant={
                        permissionMode === "individual" ? "default" : "outline"
                      }
                      className="h-auto flex-col gap-2 p-4 shadow-sm"
                      onClick={() => setPermissionMode("individual")}
                    >
                      <Users className="h-6 w-6" />
                      <div className="text-center">
                        <div className="font-medium">Theo người dùng</div>
                        <div className="text-muted-foreground text-sm">
                          Chọn người dùng cụ thể
                        </div>
                      </div>
                    </Button>
                  </div>
                </div>

                {permissionMode === "department" ? (
                  <div>
                    <Label className="font-medium text-base">
                      Chọn phòng ban
                    </Label>
                    <div className="mt-3 max-h-60 space-y-3 overflow-y-auto">
                      {departments.map((dept) => (
                        <div
                          key={dept.id}
                          className="flex items-center space-x-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50"
                        >
                          <Checkbox
                            id={`dept-${dept.id}`}
                            checked={selectedDepartments.includes(dept.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedDepartments([
                                  ...selectedDepartments,
                                  dept.id,
                                ]);
                              } else {
                                setSelectedDepartments(
                                  selectedDepartments.filter(
                                    (id) => id !== dept.id,
                                  ),
                                );
                              }
                            }}
                          />
                          <Label
                            htmlFor={`dept-${dept.id}`}
                            className="flex-1 cursor-pointer"
                          >
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{dept.name}</span>
                              <Badge variant="secondary" className="shadow-sm">
                                {dept.userCount} học viên
                              </Badge>
                            </div>
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div>
                    <Label className="font-medium text-base">
                      Tìm kiếm người dùng
                    </Label>
                    <div className="mt-3 space-y-3">
                      <div className="relative">
                        <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 transform text-gray-400" />
                        <Input
                          placeholder="Nhập tên hoặc email..."
                          value={userSearchTerm}
                          onChange={(e) => setUserSearchTerm(e.target.value)}
                          className="pl-10 shadow-sm"
                        />
                      </div>
                      <div className="max-h-60 space-y-2 overflow-y-auto">
                        {filteredUsers.map((user) => (
                          <div
                            key={user.id}
                            className="flex items-center space-x-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50"
                          >
                            <Checkbox
                              id={`user-${user.id}`}
                              checked={selectedUsers.includes(user.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedUsers([...selectedUsers, user.id]);
                                } else {
                                  setSelectedUsers(
                                    selectedUsers.filter(
                                      (id) => id !== user.id,
                                    ),
                                  );
                                }
                              }}
                            />
                            <Avatar className="h-8 w-8">
                              <AvatarImage
                                src={`/placeholder.svg?height=32&width=32&text=${user.name.charAt(0)}`}
                              />
                              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 font-semibold text-white">
                                {user.name.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <Label
                              htmlFor={`user-${user.id}`}
                              className="flex-1 cursor-pointer"
                            >
                              <div>
                                <div className="font-medium">{user.name}</div>
                                <div className="text-gray-600 text-sm">
                                  {user.email} • {user.department}
                                </div>
                              </div>
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex justify-end gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                    className="shadow-sm"
                  >
                    Hủy
                  </Button>
                  <Button onClick={handleSavePermissions} className="shadow-sm">
                    Lưu thay đổi
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent className="space-y-8 p-6">
        {/* Department Permissions */}
        <div>
          <h3 className="mb-4 font-semibold text-gray-900 text-lg">
            Quyền truy cập theo phòng ban
          </h3>
          <div className="overflow-hidden rounded-lg border border-gray-200 bg-white">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Phòng ban
                  </th>
                  <th className="px-6 py-4 text-left font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Trạng thái truy cập
                  </th>
                  <th className="px-6 py-4 text-left font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Số học viên đã đăng ký
                  </th>
                  <th className="px-6 py-4 text-left font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Thao tác
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {departmentPermissions.map((perm) => (
                  <tr
                    key={perm.id}
                    className="transition-colors hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 font-semibold text-gray-900">
                      {perm.department}
                    </td>
                    <td className="px-6 py-4">
                      <Badge
                        className={`shadow-sm ${
                          perm.hasAccess
                            ? "border-green-200 bg-green-100 text-green-800"
                            : "border-gray-200 bg-gray-100 text-gray-800"
                        }`}
                        variant="outline"
                      >
                        {perm.hasAccess
                          ? "Có quyền truy cập"
                          : "Không có quyền"}
                      </Badge>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-900 text-sm">
                          {perm.enrolledCount} học viên
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <Button
                        variant="outline"
                        size="sm"
                        className={`shadow-sm ${
                          perm.hasAccess
                            ? "border-red-200 text-red-700 hover:bg-red-50"
                            : "border-green-200 text-green-700 hover:bg-green-50"
                        }`}
                      >
                        {perm.hasAccess ? "Thu hồi quyền" : "Cấp quyền"}
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Individual Permissions */}
        <div>
          <h3 className="mb-4 font-semibold text-gray-900 text-lg">
            Quyền truy cập cá nhân
          </h3>
          <div className="space-y-3">
            {individualPermissions.map((perm) => (
              <div
                key={perm.id}
                className="flex items-center justify-between rounded-lg border border-gray-200 p-4 transition-colors hover:bg-gray-50"
              >
                <div className="flex items-center gap-4">
                  <Avatar className="h-12 w-12 shadow-sm ring-2 ring-white">
                    <AvatarImage
                      src={`/placeholder.svg?height=48&width=48&text=${perm.name?.charAt(0)}`}
                    />
                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 font-semibold text-white">
                      {perm.name?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-semibold text-gray-900">
                      {perm.name}
                    </div>
                    <div className="text-gray-600 text-sm">{perm.email}</div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Badge variant="outline" className="shadow-sm">
                    {perm.department}
                  </Badge>
                  <Badge
                    className={`shadow-sm ${
                      perm.hasAccess
                        ? "border-green-200 bg-green-100 text-green-800"
                        : "border-gray-200 bg-gray-100 text-gray-800"
                    }`}
                    variant="outline"
                  >
                    {perm.hasAccess ? "Có quyền truy cập" : "Không có quyền"}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    className={`shadow-sm ${
                      perm.hasAccess
                        ? "border-red-200 text-red-700 hover:bg-red-50"
                        : "border-green-200 text-green-700 hover:bg-green-50"
                    }`}
                  >
                    {perm.hasAccess ? "Thu hồi quyền" : "Cấp quyền"}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
