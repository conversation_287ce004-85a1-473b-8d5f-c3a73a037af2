import { Award, BarChart3, TrendingUp, Users } from "lucide-react";
import { StatsCard } from "@/components/ui/stats-card";
import type { CourseData } from "./types";

interface CourseStatsProps {
  courseData: CourseData;
}

export function CourseStats({ courseData }: CourseStatsProps) {
  const formatValue = (value: number | string) => {
    if (typeof value === "number" && value > 999) {
      return `${(value / 1000).toFixed(1)}k`;
    }
    return value;
  };

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
      <StatsCard
        title="Tổng số đăng ký"
        value={formatValue(courseData.enrolled)}
        badge={{
          variant: "outline",
          icon: <Users className="h-4 w-4" />,
          text: "Học viên",
        }}
        footer={{
          title: "Tổng số đăng ký",
          subtitle: "Số lượng học viên đã đăng ký khóa học",
          icon: <Users className="h-4 w-4" />,
        }}
        className="border-blue-100 bg-gradient-to-t from-blue-50 to-card"
      />

      <StatsCard
        title="Đã hoàn thành"
        value={formatValue(courseData.completed)}
        badge={{
          variant: "secondary",
          icon: <Award className="h-4 w-4" />,
          text: "Hoàn thành",
        }}
        footer={{
          title: "Học viên đã hoàn thành",
          subtitle: "Số lượng học viên đã hoàn thành khóa học",
          icon: <Award className="h-4 w-4" />,
        }}
        className="border-green-100 bg-gradient-to-t from-green-50 to-card"
      />

      <StatsCard
        title="Tỷ lệ hoàn thành"
        value={`${courseData.completionRate}%`}
        badge={{
          variant: "outline",
          icon: <TrendingUp className="h-4 w-4" />,
          text: courseData.completionRate > 80 ? "Tốt" : "Cần cải thiện",
        }}
        footer={{
          title: courseData.completionRate > 80 ? "Tỷ lệ tốt" : "Cần cải thiện",
          subtitle: "Phần trăm học viên hoàn thành khóa học",
          icon: <TrendingUp className="h-4 w-4" />,
        }}
        className="border-orange-100 bg-gradient-to-t from-orange-50 to-card"
      />

      <StatsCard
        title="Điểm trung bình"
        value={courseData.averageScore}
        badge={{
          variant: "outline",
          icon: <BarChart3 className="h-4 w-4" />,
          text:
            courseData.averageScore >= 8
              ? "Xuất sắc"
              : courseData.averageScore >= 6
                ? "Tốt"
                : "Trung bình",
        }}
        footer={{
          title: "Hiệu suất học tập",
          subtitle: "Điểm số trung bình của học viên",
          icon: <BarChart3 className="h-4 w-4" />,
        }}
        className="border-purple-100 bg-gradient-to-t from-purple-50 to-card"
      />
    </div>
  );
}
