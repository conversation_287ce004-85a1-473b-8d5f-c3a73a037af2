import { Search } from "lucide-react";
import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { SortableHeader } from "@/components/ui/sortable-header";
import { useSortableTable } from "@/hooks/use-table-sorting";
import type { Student } from "./types";

interface StudentsTableProps {
  students: Student[];
}

export function StudentsTable({ students }: StudentsTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const filteredStudents = students.filter(
    (student) =>
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.department.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const { sortedData, handleSort, getSortIcon } = useSortableTable({
    data: filteredStudents,
    defaultSort: { column: "name", direction: "asc" },
  });

  const totalPages = Math.ceil(sortedData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedStudents = sortedData.slice(startIndex, endIndex);

  const pageNumbers = Array.from(
    { length: Math.min(5, totalPages) },
    (_, i) => i + 1,
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Hoàn thành":
        return "bg-green-100 text-green-800 border-green-200";
      case "Đang học":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "Tạm dừng":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="border-gray-100 border-b">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="font-semibold text-lg">
              Học viên đã đăng ký
            </CardTitle>
            <CardDescription className="mt-1">
              Danh sách học viên hiện tại và tiến độ học tập của họ
            </CardDescription>
          </div>
          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 transform text-gray-400" />
              <Input
                placeholder="Tìm kiếm học viên..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64 pl-10 shadow-sm"
              />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full min-w-[60rem] table-fixed">
            <thead className="border-gray-200 border-b bg-gray-50">
              <tr>
                <th className="w-[20%] px-6 py-4 text-left sm:w-[25%]">
                  <SortableHeader
                    sortDirection={getSortIcon("name")}
                    onSort={() => handleSort("name")}
                  >
                    Học viên
                  </SortableHeader>
                </th>
                <th className="w-[10%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("department")}
                    onSort={() => handleSort("department")}
                  >
                    Phòng ban
                  </SortableHeader>
                </th>
                <th className="w-[10%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("enrolledDate")}
                    onSort={() => handleSort("enrolledDate")}
                  >
                    Ngày đăng ký
                  </SortableHeader>
                </th>
                <th className="w-[14%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("progress")}
                    onSort={() => handleSort("progress")}
                  >
                    Tiến độ
                  </SortableHeader>
                </th>
                <th className="w-[10%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("status")}
                    onSort={() => handleSort("status")}
                  >
                    Trạng thái
                  </SortableHeader>
                </th>
                <th className="w-[8%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("score")}
                    onSort={() => handleSort("score")}
                  >
                    Điểm số
                  </SortableHeader>
                </th>
                <th className="w-[10%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("studyTime")}
                    onSort={() => handleSort("studyTime")}
                  >
                    Thời gian học
                  </SortableHeader>
                </th>
                <th className="w-[13%] px-6 py-4 text-left">
                  <SortableHeader
                    sortDirection={getSortIcon("lastAccess")}
                    onSort={() => handleSort("lastAccess")}
                  >
                    Truy cập gần nhất
                  </SortableHeader>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {paginatedStudents.map((student) => (
                <tr
                  key={student.id}
                  className="transition-colors hover:bg-gray-50"
                >
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10 ring-2 ring-white">
                        <AvatarImage
                          src={`/placeholder.svg?height=40&width=40&text=${student.name.charAt(
                            0,
                          )}`}
                        />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 font-semibold text-white">
                          {student.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-semibold text-gray-900">
                          {student.name}
                        </p>
                        <p className="text-gray-500 text-sm">{student.role}</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <Badge variant="outline" className="shadow-sm">
                      {student.department}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 text-gray-600 text-sm">
                    {new Date(student.enrolledDate).toLocaleDateString("vi-VN")}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <Progress value={student.progress} className="h-2 w-20" />
                      <span className="min-w-[3rem] font-semibold text-gray-900 text-sm">
                        {student.progress}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <Badge
                      className={`${getStatusColor(student.status)} shadow-sm`}
                      variant="outline"
                    >
                      {student.status}
                    </Badge>
                  </td>
                  <td className="px-6 py-4">
                    <span className="font-semibold text-gray-900 text-sm">
                      {student.score} điểm
                    </span>
                  </td>
                  <td className="px-6 py-4 text-gray-600 text-sm">
                    {student.studyTime}
                  </td>
                  <td className="px-6 py-4 text-gray-500 text-sm">
                    {student.lastAccess}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {totalPages > 1 && (
          <div className="flex flex-col items-center justify-between gap-2 border-gray-200 border-t bg-gray-50 px-6 py-4 sm:flex-row">
            <div className="flex items-center gap-2 text-gray-600 text-sm">
              <span>Hiển thị:</span>
              <select
                value={itemsPerPage.toString()}
                onChange={(e) => {
                  setItemsPerPage(parseInt(e.target.value));
                  setCurrentPage(1);
                }}
                className="h-8 w-20 rounded-md border border-gray-300 bg-white px-2 py-1 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
              </select>
              <span>mục</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="hidden text-gray-600 text-sm sm:inline">
                Kết quả: {startIndex + 1} -{" "}
                {Math.min(endIndex, sortedData.length)} trên {sortedData.length}
              </span>
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="shadow-sm"
                >
                  ‹
                </Button>
                {pageNumbers.map((pageNum) => (
                  <Button
                    key={pageNum}
                    variant={pageNum === currentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(pageNum)}
                    className="h-8 w-8 shadow-sm"
                  >
                    {pageNum}
                  </Button>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="shadow-sm"
                >
                  ›
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
