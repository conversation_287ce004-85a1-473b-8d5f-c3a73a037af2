import { Search } from "lucide-react";
import React from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CourseFilters as CourseFiltersType } from "../../routes/dashboard/courses/types";
import { DEPARTMENTS, INSTRUCTORS } from "../../utils/constants";

interface CourseFiltersProps {
  filters: CourseFiltersType;
  onFilterChange: (type: keyof CourseFiltersType, value: string) => void;
}

export function CourseFilters({ filters, onFilterChange }: CourseFiltersProps) {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <div className="relative">
        <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 transform text-muted-foreground" />
        <Input
          placeholder="Tì<PERSON> kiếm theo tên khóa học..."
          value={filters.searchTerm}
          onChange={(e) => onFilterChange("searchTerm", e.target.value)}
          className="w-80 pl-10"
        />
      </div>

      <Select
        value={filters.department}
        onValueChange={(value) => onFilterChange("department", value)}
      >
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Lọc theo phòng ban" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tất cả phòng ban</SelectItem>
          {DEPARTMENTS.map((dept) => (
            <SelectItem key={dept} value={dept}>
              {dept}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select
        value={filters.status}
        onValueChange={(value) => onFilterChange("status", value)}
      >
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Lọc theo trạng thái" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tất cả trạng thái</SelectItem>
          <SelectItem value="published">Đã xuất bản</SelectItem>
          <SelectItem value="draft">Bản nháp</SelectItem>
          <SelectItem value="archived">Đã lưu trữ</SelectItem>
        </SelectContent>
      </Select>

      <Select
        value={filters.instructor}
        onValueChange={(value) => onFilterChange("instructor", value)}
      >
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Lọc theo giảng viên" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tất cả giảng viên</SelectItem>
          {INSTRUCTORS.map((instructor) => (
            <SelectItem key={instructor} value={instructor}>
              {instructor}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select
        value={filters.sortBy}
        onValueChange={(value) => onFilterChange("sortBy", value)}
      >
        <SelectTrigger className="w-48">
          <SelectValue placeholder="Sắp xếp theo" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="date">Ngày cập nhật</SelectItem>
          <SelectItem value="popularity">Độ phổ biến</SelectItem>
          <SelectItem value="rating">Đánh giá</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
