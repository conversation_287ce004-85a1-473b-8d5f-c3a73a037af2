import { Calendar, Edit, Eye, Star, Trash2, User, Users } from "lucide-react";
import React from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { TableCell, TableRow } from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Course } from "../../routes/dashboard/courses/types";

interface CourseTableRowProps {
  course: Course;
  onViewCourse: (courseId: number) => void;
  getStatusColor: (status: Course["status"]) => string;
  getStatusText: (status: Course["status"]) => string;
  getRequirementColor: (requirement: Course["requirement"]) => string;
  getRequirementText: (requirement: Course["requirement"]) => string;
}

export function CourseTableRow({
  course,
  onViewCourse,
  getStatusColor,
  getStatusText,
  getRequirementColor,
  getRequirementText,
}: CourseTableRowProps) {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-3 w-3 ${
          i < Math.floor(rating)
            ? "fill-yellow-400 text-yellow-400"
            : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <TableRow
      className="cursor-pointer hover:bg-muted/50"
      onClick={() => onViewCourse(course.id)}
    >
      <TableCell>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="hover:text-blue-600">
              <div className="font-medium">{course.title}</div>
              <div className="mb-2 text-muted-foreground text-sm">
                {course.description}
              </div>
              {course.status === "published" && (
                <div className="space-y-1">
                  <div className="flex justify-between text-muted-foreground text-xs">
                    <span>Tỷ lệ hoàn thành</span>
                    <span>{course.completionRate}%</span>
                  </div>
                  <Progress value={course.completionRate} className="h-1" />
                </div>
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent side="right" className="max-w-sm p-4">
            <div className="space-y-3">
              <div>
                <h4 className="font-semibold">{course.title}</h4>
                <p className="text-muted-foreground text-sm">
                  {course.description}
                </p>
              </div>
              {course.status === "published" && (
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Tỷ lệ hoàn thành:</span>
                    <span className="font-medium">
                      {course.completionRate}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Điểm trung bình:</span>
                    <span className="font-medium">{course.averageScore}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Số đánh giá:</span>
                    <span className="font-medium">{course.totalRatings}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Đánh giá trung bình:</span>
                    <div className="flex items-center gap-1">
                      <div className="flex">
                        {renderStars(course.averageRating)}
                      </div>
                      <span className="font-medium text-xs">
                        ({course.averageRating})
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span>Thời lượng:</span>
                    <span className="font-medium">{course.category}</span>
                  </div>
                </div>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{course.instructor}</span>
        </div>
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{course.creationDate}</span>
        </div>
      </TableCell>
      <TableCell>
        <Badge className={getStatusColor(course.status)}>
          {getStatusText(course.status)}
        </Badge>
      </TableCell>
      <TableCell>
        <Badge
          className={getRequirementColor(course.requirement)}
          variant="outline"
        >
          {getRequirementText(course.requirement)}
        </Badge>
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span>{course.students}</span>
        </div>
      </TableCell>
      <TableCell onClick={(e) => e.stopPropagation()}>
        <div className="flex items-center gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-blue-100"
                onClick={() => onViewCourse(course.id)}
              >
                <Eye className="h-4 w-4 text-blue-600" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Xem chi tiết</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-green-100"
              >
                <Edit className="h-4 w-4 text-green-600" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Chỉnh sửa</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-red-100"
              >
                <Trash2 className="h-4 w-4 text-red-600" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Xóa</TooltipContent>
          </Tooltip>
        </div>
      </TableCell>
    </TableRow>
  );
}
