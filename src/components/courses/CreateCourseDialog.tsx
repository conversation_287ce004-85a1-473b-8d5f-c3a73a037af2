import { FileText, Layers, Plus, Upload } from "lucide-react";
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface CreateCourseDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateCourseDialog({
  isOpen,
  onOpenChange,
}: CreateCourseDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button className="bg-black text-white hover:bg-black/90">
          <Plus className="mr-2 h-4 w-4" />
          Tạo khóa học mới
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle><PERSON><PERSON><PERSON> kh<PERSON><PERSON> học mới</DialogTitle>
          <DialogDescription>Ch<PERSON><PERSON> cách thức tạo khóa học</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <Button
            variant="outline"
            className="h-auto w-full justify-start bg-transparent p-4"
            onClick={() => onOpenChange(false)}
          >
            <FileText className="mr-3 h-5 w-5" />
            <div className="text-left">
              <div className="font-medium">Tạo từ đầu</div>
              <div className="text-muted-foreground text-sm">
                Tạo khóa học hoàn toàn mới
              </div>
            </div>
          </Button>
          <Button
            variant="outline"
            className="h-auto w-full justify-start bg-transparent p-4"
            onClick={() => onOpenChange(false)}
          >
            <Upload className="mr-3 h-5 w-5" />
            <div className="text-left">
              <div className="font-medium">Import từ file</div>
              <div className="text-muted-foreground text-sm">
                Tải lên nội dung từ file có sẵn
              </div>
            </div>
          </Button>
          <Button
            variant="outline"
            className="h-auto w-full justify-start bg-transparent p-4"
            onClick={() => onOpenChange(false)}
          >
            <Layers className="mr-3 h-5 w-5" />
            <div className="text-left">
              <div className="font-medium">Chọn template</div>
              <div className="text-muted-foreground text-sm">
                Sử dụng mẫu có sẵn
              </div>
            </div>
          </Button>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
