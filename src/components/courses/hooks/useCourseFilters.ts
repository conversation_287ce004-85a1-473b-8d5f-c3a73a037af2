import { useMemo, useState } from "react";
import { Course, CourseFilters } from "@/routes/dashboard/courses/types";

export function useCourseFilters(courses: Course[]) {
  const [filters, setFilters] = useState<CourseFilters>({
    searchTerm: "",
    status: "all",
    department: "all",
    instructor: "all",
    sortBy: "date",
  });

  const filteredCourses = useMemo(() => {
    return courses
      .filter((course) => {
        const matchesSearch = course.title
          .toLowerCase()
          .includes(filters.searchTerm.toLowerCase());
        const matchesStatus =
          filters.status === "all" || course.status === filters.status;
        const matchesDepartment =
          filters.department === "all" ||
          course.department === filters.department;
        const matchesInstructor =
          filters.instructor === "all" ||
          course.instructor === filters.instructor;
        return (
          matchesSearch &&
          matchesStatus &&
          matchesDepartment &&
          matchesInstructor
        );
      })
      .sort((a, b) => {
        switch (filters.sortBy) {
          case "popularity":
            return b.students - a.students;
          case "rating":
            return b.averageRating - a.averageRating;
          default:
            return (
              new Date(b.lastUpdated).getTime() -
              new Date(a.lastUpdated).getTime()
            );
        }
      });
  }, [courses, filters]);

  const updateFilter = (type: keyof CourseFilters, value: string) => {
    setFilters((prev) => ({ ...prev, [type]: value }));
  };

  return {
    filters,
    filteredCourses,
    updateFilter,
  };
}
