import { <PERSON><PERSON>elector, CardTemplate } from "@aicademy/shared-card-templates";
import { Laptop, Smartphone, Tablet, X } from "lucide-react";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import {
  AddSectionDialog,
  DeleteConfirmDialog,
  FloatingComponentPicker,
  LessonHeader,
  LessonSidebar,
  SlideRenderer,
} from "./lesson-editor/components";
import { useJotaiLessonState, useLessonState } from "./lesson-editor/hooks";
import type {
  DeleteConfirm,
  LayoutComponent,
  LayoutTemplate,
} from "./lesson-editor/types";
import {
  calculateSmartPickerPosition,
  countComponentsInSection,
} from "./lesson-editor/utils";

export default function LessonEditor() {
  // Jotai state management (shared across components)
  const {
    lesson: currentLesson,
    currentSlide,
    currentSlideIndex,
    setCurrentSlideIndex,
    previewMode,
    setPreviewMode,
    editingComponent,
    setEditingComponent,
    draggedComponent,
    dragOverZone,
    selectedZone,
    showFloatingPicker,
    pickerPosition,
    updateLesson,
    addComponentToZone: addComponentToZoneJotai,
    addSectionToSlide: addSectionToSlideJotai,
    changeSectionLayout,
    updateComponentContent: updateComponentContentJotai,
    selectZone,
    closePicker,
    startDrag,
    dragOver,
    endDrag,
    performDragDrop,
  } = useJotaiLessonState();

  // Legacy lesson state for complex operations (will be migrated gradually)
  const {
    addSlide,
    removeSlide,
    removeSectionFromSlide,
    removeComponentFromZone,
  } = useLessonState();

  // Local UI state (only used in this component)
  const [showSectionDialog, setShowSectionDialog] = useState(false);
  const [deleteConfirm, setDeleteConfirm] = useState<DeleteConfirm | null>(
    null,
  );
  const [showCardSelector, setShowCardSelector] = useState(false);
  const [selectedZoneForCard, setSelectedZoneForCard] = useState<{
    sectionId: string;
    zoneId: string;
  } | null>(null);

  // Event handlers
  const handleAddSlide = () => {
    const newLesson = addSlide();
    updateLesson(newLesson);
  };

  const handleDeleteSlide = (slideIndex: number) => {
    confirmDeleteSlide(slideIndex);
  };

  const handleAddSection = (template: LayoutTemplate) => {
    // Use Jotai atom directly to avoid state conflicts with drag-drop
    addSectionToSlideJotai(template);
  };

  const handleDeleteSection = (sectionId: string) => {
    confirmDeleteSection(sectionId);
  };

  const handleAddComponent = (
    sectionId: string,
    zoneId: string,
    type: LayoutComponent["type"],
  ) => {
    if (type === "card") {
      setSelectedZoneForCard({ sectionId, zoneId });
      setShowCardSelector(true);
    } else {
      // Use Jotai atom directly to avoid state conflicts with drag-drop
      addComponentToZoneJotai({ sectionId, zoneId, type });
    }
  };

  const handleDeleteComponent = (
    sectionId: string,
    zoneId: string,
    componentId: string,
  ) => {
    confirmDeleteComponent(sectionId, zoneId, componentId);
  };

  const handleUpdateComponent = (
    sectionId: string,
    zoneId: string,
    componentId: string,
    content: string,
  ) => {
    // Use Jotai atom directly to avoid state conflicts with drag-drop
    updateComponentContentJotai({
      sectionId,
      zoneId,
      componentId,
      newContent: content,
    });
  };

  const handleZoneClick = (
    event: React.MouseEvent,
    sectionId: string,
    zoneId: string,
  ) => {
    event.stopPropagation();

    const rect = event.currentTarget.getBoundingClientRect();
    const position = calculateSmartPickerPosition(rect);

    selectZone({ sectionId, zoneId, position });
  };

  const handlePickerClose = () => {
    closePicker();
  };

  const toggleEditMode = (componentId: string) => {
    setEditingComponent(editingComponent === componentId ? null : componentId);
  };

  // Helper functions for delete confirmations
  const confirmDeleteSlide = (slideIndex: number) => {
    if (currentLesson.slides.length <= 1) return;

    const slide = currentLesson.slides[slideIndex];
    const componentCount = slide.sections.reduce(
      (total, section) => total + countComponentsInSection(section),
      0,
    );

    setDeleteConfirm({
      type: "slide",
      data: { slideIndex },
      title: "Delete Slide",
      description: `Are you sure you want to delete "Slide ${
        slideIndex + 1
      }"? This will also delete ${slide.sections.length} section${
        slide.sections.length !== 1 ? "s" : ""
      } and ${componentCount} component${
        componentCount !== 1 ? "s" : ""
      } inside it. This action cannot be undone.`,
    });
  };

  const confirmDeleteSection = (sectionId: string) => {
    const section = currentSlide.sections.find((s) => s.id === sectionId);
    if (!section) return;

    const componentCount = countComponentsInSection(section);

    setDeleteConfirm({
      type: "section",
      data: { sectionId },
      title: "Delete Section",
      description: `Are you sure you want to delete "${
        section.name
      }"? This will also delete ${componentCount} component${
        componentCount !== 1 ? "s" : ""
      } inside it. This action cannot be undone.`,
    });
  };

  const confirmDeleteComponent = (
    sectionId: string,
    zoneId: string,
    componentId: string,
  ) => {
    const component = currentSlide.sections
      .find((s) => s.id === sectionId)
      ?.zones.find((z) => z.id === zoneId)
      ?.components.find((c) => c.id === componentId);

    if (!component) return;

    setDeleteConfirm({
      type: "component",
      data: { sectionId, zoneId, componentId },
      title: "Delete Component",
      description: `Are you sure you want to delete this ${component.type} component? This action cannot be undone.`,
    });
  };

  const handleConfirmDelete = () => {
    if (!deleteConfirm) return;

    const { type, data } = deleteConfirm;

    switch (type) {
      case "component": {
        const componentLesson = removeComponentFromZone(
          data.sectionId,
          data.zoneId,
          data.componentId,
        );
        updateLesson(componentLesson);
        break;
      }
      case "section": {
        const sectionLesson = removeSectionFromSlide(data.sectionId);
        updateLesson(sectionLesson);
        break;
      }
      case "slide": {
        const slideLesson = removeSlide(data.slideIndex);
        updateLesson(slideLesson);
        break;
      }
    }

    setDeleteConfirm(null);
  };

  const getPreviewContainerClass = () => {
    if (!previewMode) return "";
    switch (previewMode) {
      case "desktop":
        return "max-w-7xl p-8 gap-4";
      case "tablet":
        return "max-w-4xl p-6"; // ~1024px
      case "mobile":
        return "max-w-sm p-4"; // ~375px
      default:
        return "p-2";
    }
  };

  return (
    <div className="relative min-h-screen bg-gray-50 pt-[80px]">
      <LessonHeader
        lesson={currentLesson}
        previewMode={previewMode}
        onSetPreviewMode={setPreviewMode}
        onSave={() => console.log("Save lesson")}
      />

      {previewMode && (
        <div className="-translate-x-1/2 fixed top-[80px] left-1/2 z-20 transform rounded-b-lg border border-t-0 bg-white p-1 shadow-lg">
          <div className="flex items-center gap-1">
            <Button
              variant={previewMode === "mobile" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setPreviewMode("mobile")}
              className="h-8 px-3"
              title="Mobile preview"
            >
              <Smartphone className="h-4 w-4" />
            </Button>
            <Button
              variant={previewMode === "tablet" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setPreviewMode("tablet")}
              className="h-8 px-3"
              title="Tablet preview"
            >
              <Tablet className="h-4 w-4" />
            </Button>
            <Button
              variant={previewMode === "desktop" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setPreviewMode("desktop")}
              className="h-8 px-3"
              title="Desktop preview"
            >
              <Laptop className="h-4 w-4" />
            </Button>
            <div className="mx-2 h-6 w-px bg-gray-200" />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPreviewMode(false)}
              className="h-8 px-3"
              title="Exit preview"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      <div className="flex pt-0">
        {previewMode === false && (
          <LessonSidebar
            lesson={currentLesson}
            currentSlide={currentSlide}
            currentSlideIndex={currentSlideIndex}
            onSlideSelect={setCurrentSlideIndex}
            onAddSlide={handleAddSlide}
            onDeleteSlide={handleDeleteSlide}
          />
        )}

        <main
          className={
            previewMode
              ? "mx-auto flex-1 bg-gray-200 py-8 transition-all duration-300"
              : "ml-80 flex-1 bg-gray-50 p-6"
          }
          onClick={(_e) => {
            // Only close picker if not dragging
            if (!draggedComponent) {
              closePicker();
            }
          }}
        >
          <div
            className={
              previewMode
                ? `mx-auto bg-white transition-all duration-300 ${getPreviewContainerClass()} flex flex-col gap-6`
                : "space-y-8"
            }
          >
            {previewMode === false && currentSlide.sections.length > 0 && (
              <div className="flex items-center justify-between">
                <h2 className="font-sans font-semibold text-gray-900 text-xl">
                  {currentSlide.name}
                </h2>
                <p className="text-gray-600 text-sm">
                  Click zones to add components • Drag to reorder • Click edit
                  for rich text
                </p>
              </div>
            )}

            <SlideRenderer
              slide={currentSlide}
              slideIndex={currentSlideIndex}
              previewMode={previewMode}
              onAddSection={() => setShowSectionDialog(true)}
              onDeleteSection={handleDeleteSection}
              onZoneClick={handleZoneClick}
              onAddComponent={handleAddComponent}
              onDeleteComponent={handleDeleteComponent}
              onUpdateComponent={handleUpdateComponent}
              onToggleEdit={toggleEditMode}
              editingComponent={editingComponent}
              draggedComponent={draggedComponent}
              dragOverZone={dragOverZone}
              onDragStart={(_e, sectionId, zoneId, componentId, index) => {
                startDrag({ sectionId, zoneId, componentId, index });
              }}
              onDragOver={(e) => {
                e.preventDefault();
                e.stopPropagation();
                e.dataTransfer.dropEffect = "move";
              }}
              onDragOverWithFeedback={(e, sectionId, zoneId, index) => {
                e.preventDefault();
                e.stopPropagation();
                e.dataTransfer.dropEffect = "move";
                dragOver({ sectionId, zoneId, index });
              }}
              onDrop={(e, sectionId, zoneId, index) => {
                e.preventDefault();
                e.stopPropagation();
                performDragDrop({
                  targetSectionId: sectionId,
                  targetZoneId: zoneId,
                  targetIndex: index,
                });
              }}
              onDragEnd={() => {
                endDrag();
              }}
              onDragLeave={(e) => {
                if (!e.currentTarget.contains(e.relatedTarget as Node)) {
                  dragOver({ sectionId: "", zoneId: "", index: -1 });
                }
              }}
              onChangeLayout={(
                slideIndex,
                sectionIndex,
                newTemplate,
                migrationOption,
              ) => {
                changeSectionLayout({
                  slideIndex,
                  sectionIndex,
                  newTemplate,
                  migrationOption,
                });
              }}
            />
          </div>
        </main>
      </div>

      <AddSectionDialog
        isOpen={showSectionDialog}
        onClose={() => setShowSectionDialog(false)}
        onAddSection={handleAddSection}
      />

      <DeleteConfirmDialog
        deleteConfirm={deleteConfirm}
        onConfirm={handleConfirmDelete}
        onCancel={() => setDeleteConfirm(null)}
      />

      <FloatingComponentPicker
        isVisible={showFloatingPicker}
        selectedZone={selectedZone}
        position={pickerPosition}
        onAddComponent={handleAddComponent}
        onClose={handlePickerClose}
      />

      <Dialog open={showCardSelector} onOpenChange={setShowCardSelector}>
        <DialogContent className="!max-w-max">
          <CardSelector
            onCardSelect={(cardProps) => {
              if (selectedZoneForCard) {
                addComponentToZoneJotai({
                  ...selectedZoneForCard,
                  type: "card",
                  content: cardProps as any,
                });
                setShowCardSelector(false);
                setSelectedZoneForCard(null);
              }
            }}
            onCancel={() => {
              setShowCardSelector(false);
              setSelectedZoneForCard(null);
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
