import { Book, Palette, Star } from "lucide-react";
import { PropsWithChildren, ReactNode, useState } from "react";
import { HexColorPicker } from "react-colorful";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

type CardIcon = "book" | "star" | "palette";

interface CardParams {
  icon: CardIcon;
  title: string;
  content: string[];
  bg: string;
}

interface CardSelectorProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onCardSelect: (params: CardParams) => void;
  initialParams?: CardParams;
  onCancel?: () => void;
}

export const CardSelector = ({
  children,
  onCardSelect,
  initialParams,
  onCancel,
  open,
  onOpenChange,
}: CardSelectorProps & PropsWithChildren) => {
  // Nếu có initialParams thì dùng để edit, còn không thì chọn mới
  const [selectedIdx, setSelectedIdx] = useState<number | null>(
    initialParams ? 0 : null,
  );
  const [params, setParams] = useState<CardParams>(
    initialParams || {
      icon: "book",
      title: "",
      content: [""],
      bg: "#f8fafc",
    },
  );

  // Khi chọn card, set params theo card đó
  const handleSelect = (idx: number) => {
    setSelectedIdx(idx);
    const selectedCard = cardData[idx];
    setParams({
      icon: selectedCard.icon,
      title: selectedCard.title,
      content: [...selectedCard.content],
      bg: selectedCard.bg,
    });
  };

  // Khi xác nhận, truyền params ra ngoài
  const handleConfirm = () => {
    onCardSelect(params);
  };

  // Khi bấm Cancel, gọi hàm onCancel nếu có
  const handleCancel = () => {
    if (onCancel) onCancel();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {children}
      <DialogContent className="!max-w-[1--dvw]">
        {/* Nếu chưa chọn card thì show danh sách card, nếu đã chọn thì chỉ show phần tùy chỉnh và preview */}
        {selectedIdx === null ? (
          <>
            <DialogHeader>
              <DialogTitle>Chọn một mẫu Card</DialogTitle>
            </DialogHeader>
            <div className="flex flex-wrap justify-center gap-8 p-4">
              {cardData.map((card, idx) => (
                <Card
                  key={card.type}
                  onClick={() => handleSelect(idx)}
                  className="w-[240px] cursor-pointer text-center transition-transform hover:scale-105"
                  style={{ background: card.bg }}
                >
                  <CardHeader className="items-center">
                    {iconMap[card.icon]}
                    <CardTitle>{card.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="m-0 list-none p-0">
                      {card.content.map((c, i) => (
                        <li
                          key={i}
                          className="mb-1 text-muted-foreground text-sm"
                        >
                          {c}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>Chỉnh sửa Card</DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 gap-8 p-4 md:grid-cols-2 md:items-start">
              {/* Edit form */}
              <div className="space-y-4">
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="icon">Icon</Label>
                  <Select
                    value={params.icon}
                    onValueChange={(value) =>
                      setParams((p) => ({ ...p, icon: value as CardIcon }))
                    }
                  >
                    <SelectTrigger id="icon" className="w-[180px]">
                      <SelectValue placeholder="Chọn icon" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="book">Book</SelectItem>
                      <SelectItem value="star">Star</SelectItem>
                      <SelectItem value="palette">Palette</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    type="text"
                    value={params.title}
                    onChange={(e) =>
                      setParams((p) => ({ ...p, title: e.target.value }))
                    }
                  />
                </div>
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="content">Content</Label>
                  <Textarea
                    id="content"
                    value={params.content.join("\n")}
                    onChange={(e) =>
                      setParams((p) => ({
                        ...p,
                        content: e.target.value.split("\n"),
                      }))
                    }
                    rows={3}
                    placeholder="Mỗi dòng là một mục"
                  />
                </div>
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="bg">Background</Label>
                  <div className="my-2">
                    <HexColorPicker
                      color={params.bg}
                      onChange={(color) =>
                        setParams((p) => ({ ...p, bg: color }))
                      }
                    />
                  </div>
                  <Input
                    id="bg"
                    type="text"
                    value={params.bg}
                    onChange={(e) =>
                      setParams((p) => ({ ...p, bg: e.target.value }))
                    }
                    placeholder="CSS background, ví dụ: #fff hoặc linear-gradient(...)"
                  />
                </div>
              </div>

              {/* Preview card */}
              <div className="flex justify-center md:justify-start md:pt-8">
                <Card
                  className="w-[240px] text-center"
                  style={{ background: params.bg }}
                >
                  <CardHeader className="items-center">
                    {iconMap[params.icon]}
                    <CardTitle>{params.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="m-0 list-none p-0">
                      {params.content.map((c, i) => (
                        <li
                          key={i}
                          className="mb-1 text-muted-foreground text-sm"
                        >
                          {c}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
            <DialogFooter>
              {onCancel && (
                <Button variant="outline" type="button" onClick={handleCancel}>
                  Hủy
                </Button>
              )}
              <Button type="button" onClick={handleConfirm}>
                Lưu card này
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

const cardData: (CardParams & { type: CardIcon })[] = [
  {
    type: "book",
    icon: "book",
    title: "Sách hay",
    content: ["Tài liệu học tập", "Kiến thức nền tảng"],
    bg: "linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)",
  },
  {
    type: "star",
    icon: "star",
    title: "Nổi bật",
    content: ["Bài học nổi bật", "Gợi ý cho bạn"],
    bg: "linear-gradient(135deg, #fff7ae 0%, #ffd6e0 100%)",
  },
  {
    type: "palette",
    icon: "palette",
    title: "Sáng tạo",
    content: ["Tùy chỉnh nội dung", "Thỏa sức sáng tạo"],
    bg: "linear-gradient(135deg, #d1f5ee 0%, #f0e7ff 100%)",
  },
];

const iconMap: Record<CardIcon, ReactNode> = {
  book: <Book size={32} />,
  star: <Star size={32} />,
  palette: <Palette size={32} />,
};
