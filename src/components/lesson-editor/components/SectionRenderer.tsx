import { Grid, LayoutGrid, Trash2 } from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { LayoutSection, LayoutTemplate, PreviewMode } from "../types";
import type { DataMigrationOption } from "./LayoutChangeDialog";
import { LayoutChangeDialog } from "./LayoutChangeDialog";
import { ZoneRenderer } from "./ZoneRenderer";

interface SectionRendererProps {
  section: LayoutSection;
  sectionIndex: number;
  isLastSection: boolean;
  previewMode: PreviewMode;
  onDeleteSection: (sectionId: string) => void;
  onZoneClick: (
    event: React.MouseEvent,
    sectionId: string,
    zoneId: string,
  ) => void;
  onAddComponent: (sectionId: string, zoneId: string, type: any) => void;
  onDeleteComponent: (
    sectionId: string,
    zoneId: string,
    componentId: string,
  ) => void;
  onUpdateComponent: (
    sectionId: string,
    zoneId: string,
    componentId: string,
    content: string,
  ) => void;
  onToggleEdit: (componentId: string) => void;
  editingComponent: string | null;
  draggedComponent: any;
  dragOverZone: any;
  onDragStart: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    componentId: string,
    index: number,
  ) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDragOverWithFeedback: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    index: number,
  ) => void;
  onDrop: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    index: number,
  ) => void;
  onDragEnd: () => void;
  onDragLeave: (e: React.DragEvent) => void;
  slideIndex: number; // Add slideIndex for layout change
  onChangeLayout?: (
    slideIndex: number,
    sectionIndex: number,
    newTemplate: LayoutTemplate,
    migrationOption: DataMigrationOption,
  ) => void;
}

export const SectionRenderer = ({
  section,
  sectionIndex,
  slideIndex,
  isLastSection,
  previewMode,
  onDeleteSection,
  onZoneClick,
  onAddComponent,
  onDeleteComponent,
  onUpdateComponent,
  onToggleEdit,
  editingComponent,
  draggedComponent,
  dragOverZone,
  onDragStart,
  onDragOver,
  onDragOverWithFeedback,
  onDrop,
  onDragEnd,
  onDragLeave,
  onChangeLayout,
}: SectionRendererProps) => {
  const [showLayoutDialog, setShowLayoutDialog] = useState(false);
  const borderClass = isLastSection ? "rounded-b-lg border-b" : "border-b-0";

  const handleLayoutChange = (
    newTemplate: LayoutTemplate,
    migrationOption: DataMigrationOption,
  ) => {
    if (onChangeLayout) {
      onChangeLayout(slideIndex, sectionIndex, newTemplate, migrationOption);
    }
  };

  // Hide empty sections in preview mode
  if (previewMode) {
    const hasContent = section.zones.some(
      (zone) =>
        zone.components.length > 0 &&
        zone.components.some((component) => {
          // Check if component has meaningful content
          if (component.type === "text") {
            return (
              component.content &&
              component.content.trim() !== "" &&
              component.content !== "{}" &&
              !component.content.includes('"content":[]')
            );
          }
          if (component.type === "image") {
            return component.content && component.content.trim() !== "";
          }
          if (component.type === "video") {
            return component.content && component.content.trim() !== "";
          }
          if (component.type === "card") {
            return component.content && component.content.trim() !== "";
          }
          // Hide game/quiz components in preview
          return false;
        }),
    );

    if (!hasContent) {
      return null;
    }
  }

  return (
    <div className="group relative">
      {previewMode === false && (
        <div className="mb-3 flex items-center justify-between rounded-t-lg border-gray-200 border-b bg-gray-50/80 px-4 py-2">
          <Badge
            variant="outline"
            className="border-blue-200 bg-blue-50 text-blue-800"
          >
            <LayoutGrid className="mr-1 inline h-3 w-3" />
            {section.name}
          </Badge>
          <div className="flex items-center gap-2 opacity-0 transition-opacity group-hover:opacity-100">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowLayoutDialog(true)}
              className="text-blue-600 hover:bg-blue-50 hover:text-blue-800"
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDeleteSection(section.id)}
              className="text-red-600 hover:bg-red-50 hover:text-red-800"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      <div
        className={
          previewMode === false
            ? section.template.id === "hero-two"
              ? `space-y-6 border-gray-200 border-r border-l bg-white p-6 ${borderClass} ${
                  sectionIndex === 0 ? "rounded-t-lg border-t" : ""
                }`
              : `${
                  section.template.gridClass
                } border-gray-200 border-r border-l bg-white p-6 ${borderClass} ${
                  sectionIndex === 0 ? "rounded-t-lg border-t" : ""
                }`
            : section.template.id === "hero-two"
              ? "space-y-6" // No border, padding, bg in preview
              : section.template.gridClass // Just grid layout in preview
        }
      >
        {section.template.id === "hero-two" ? (
          <>
            <div className="col-span-2">
              <ZoneRenderer
                section={section}
                zone={section.zones[0]}
                zoneIndex={0}
                previewMode={previewMode}
                onZoneClick={onZoneClick}
                onAddComponent={onAddComponent}
                onDeleteComponent={onDeleteComponent}
                onUpdateComponent={onUpdateComponent}
                onToggleEdit={onToggleEdit}
                editingComponent={editingComponent}
                draggedComponent={draggedComponent}
                dragOverZone={dragOverZone}
                onDragStart={onDragStart}
                onDragOver={onDragOver}
                onDragOverWithFeedback={onDragOverWithFeedback}
                onDrop={onDrop}
                onDragEnd={onDragEnd}
                onDragLeave={onDragLeave}
              />
            </div>
            <div className="grid grid-cols-2 gap-6">
              {section.zones.slice(1).map((zone, index) => (
                <div key={zone.id}>
                  <ZoneRenderer
                    section={section}
                    zone={zone}
                    zoneIndex={index + 1}
                    previewMode={previewMode}
                    onZoneClick={onZoneClick}
                    onAddComponent={onAddComponent}
                    onDeleteComponent={onDeleteComponent}
                    onUpdateComponent={onUpdateComponent}
                    onToggleEdit={onToggleEdit}
                    editingComponent={editingComponent}
                    draggedComponent={draggedComponent}
                    dragOverZone={dragOverZone}
                    onDragStart={onDragStart}
                    onDragOver={onDragOver}
                    onDragOverWithFeedback={onDragOverWithFeedback}
                    onDrop={onDrop}
                    onDragEnd={onDragEnd}
                    onDragLeave={onDragLeave}
                  />
                </div>
              ))}
            </div>
          </>
        ) : (
          section.zones.map((zone, index) => (
            <div key={zone.id}>
              <ZoneRenderer
                section={section}
                zone={zone}
                zoneIndex={index}
                previewMode={previewMode}
                onZoneClick={onZoneClick}
                onAddComponent={onAddComponent}
                onDeleteComponent={onDeleteComponent}
                onUpdateComponent={onUpdateComponent}
                onToggleEdit={onToggleEdit}
                editingComponent={editingComponent}
                draggedComponent={draggedComponent}
                dragOverZone={dragOverZone}
                onDragStart={onDragStart}
                onDragOver={onDragOver}
                onDragOverWithFeedback={onDragOverWithFeedback}
                onDrop={onDrop}
                onDragEnd={onDragEnd}
                onDragLeave={onDragLeave}
              />
            </div>
          ))
        )}
      </div>

      {/* Layout Change Dialog */}
      <LayoutChangeDialog
        open={showLayoutDialog}
        onOpenChange={setShowLayoutDialog}
        currentSection={section}
        onLayoutChange={handleLayoutChange}
      />
    </div>
  );
};
