import { LayoutGrid, Plus } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import type {
  LayoutSection,
  LayoutTemplate,
  PreviewMode,
  Slide,
} from "../types";
import type { DataMigrationOption } from "./LayoutChangeDialog";
import { SectionRenderer } from "./SectionRenderer";

interface SlideRendererProps {
  slide: Slide;
  slideIndex: number;
  previewMode: PreviewMode;
  onAddSection: () => void;
  onDeleteSection: (sectionId: string) => void;
  onZoneClick: (
    event: React.MouseEvent,
    sectionId: string,
    zoneId: string,
  ) => void;
  onAddComponent: (sectionId: string, zoneId: string, type: any) => void;
  onDeleteComponent: (
    sectionId: string,
    zoneId: string,
    componentId: string,
  ) => void;
  onUpdateComponent: (
    sectionId: string,
    zoneId: string,
    componentId: string,
    content: string,
  ) => void;
  onToggleEdit: (componentId: string) => void;
  editingComponent: string | null;
  draggedComponent: any;
  dragOverZone: any;
  onDragStart: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    componentId: string,
    index: number,
  ) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDragOverWithFeedback: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    index: number,
  ) => void;
  onDrop: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    index: number,
  ) => void;
  onDragEnd: () => void;
  onDragLeave: (e: React.DragEvent) => void;
  onChangeLayout?: (
    slideIndex: number,
    sectionIndex: number,
    newTemplate: LayoutTemplate,
    migrationOption: DataMigrationOption,
  ) => void;
}

export const SlideRenderer = ({
  slide,
  slideIndex,
  previewMode,
  onAddSection,
  onDeleteSection,
  onZoneClick,
  onAddComponent,
  onDeleteComponent,
  onUpdateComponent,
  onToggleEdit,
  editingComponent,
  draggedComponent,
  dragOverZone,
  onDragStart,
  onDragOver,
  onDragOverWithFeedback,
  onDrop,
  onDragEnd,
  onDragLeave,
  onChangeLayout,
}: SlideRendererProps) => {
  if (slide.sections.length === 0) {
    return (
      <Card className="border-2 border-gray-300 border-dashed bg-white">
        <CardContent className="flex flex-col items-center justify-center py-16">
          <LayoutGrid className="mb-4 h-12 w-12 text-gray-400" />
          <h3 className="mb-2 font-medium text-gray-900 text-lg">
            Build Your Layout Slide
          </h3>
          <p className="mb-4 max-w-md text-center text-gray-600">
            Add layout sections to create rich content for this slide.
          </p>
          <Button
            onClick={onAddSection}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Your First Section
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={previewMode === false ? "space-y-1" : "space-y-4"}>
      {slide.sections.map((section, sectionIndex) => (
        <SectionRenderer
          key={section.id}
          section={section}
          sectionIndex={sectionIndex}
          slideIndex={slideIndex}
          isLastSection={slide.sections.length - 1 === sectionIndex}
          previewMode={previewMode}
          onDeleteSection={onDeleteSection}
          onZoneClick={onZoneClick}
          onAddComponent={onAddComponent}
          onDeleteComponent={onDeleteComponent}
          onUpdateComponent={onUpdateComponent}
          onToggleEdit={onToggleEdit}
          editingComponent={editingComponent}
          draggedComponent={draggedComponent}
          dragOverZone={dragOverZone}
          onDragStart={onDragStart}
          onDragOver={onDragOver}
          onDragOverWithFeedback={onDragOverWithFeedback}
          onDrop={onDrop}
          onDragEnd={onDragEnd}
          onDragLeave={onDragLeave}
          onChangeLayout={onChangeLayout}
        />
      ))}

      {/* Add Section area */}
      {previewMode === false && (
        <div
          className="group/add-section relative mt-6 flex min-h-[80px] cursor-pointer items-center justify-center rounded-lg border-2 border-gray-300 border-dashed bg-gray-50/50 py-6 transition-all hover:border-blue-400 hover:bg-blue-50/50"
          onClick={onAddSection}
        >
          <div className="flex flex-col items-center gap-2 text-gray-600 opacity-70 transition-opacity group-hover/add-section:text-blue-600 group-hover/add-section:opacity-100">
            <LayoutGrid className="h-6 w-6" />
            <span className="font-medium text-sm">Add Section</span>
            <span className="text-gray-500 text-xs group-hover/add-section:text-blue-500">
              Choose layout template
            </span>
          </div>
        </div>
      )}
    </div>
  );
};
