import { useState } from "react";
import type {
  LayoutComponent,
  LayoutSection,
  LayoutTemplate,
  Lesson,
  Slide,
} from "../types";
import { createDefaultSlide, createNewComponent } from "../utils";

export const useLessonState = (initialLesson?: Lesson) => {
  const [currentLesson, setCurrentLesson] = useState<Lesson>(
    initialLesson || {
      id: "lesson-1",
      title: "New Lesson",
      description: "Create engaging lesson content with multiple slides",
      slides: [createDefaultSlide(1)],
    }
  );
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);

  const currentSlide = currentLesson.slides[currentSlideIndex];

  const updateLesson = (updater: (lesson: Lesson) => Lesson) => {
    setCurrentLesson(updater);
  };

  const addSlide = () => {
    const newSlide = createDefaultSlide(currentLesson.slides.length + 1);
    const newLesson = {
      ...currentLesson,
      slides: [...currentLesson.slides, newSlide],
    };
    setCurrentLesson(newLesson);
    setCurrentSlideIndex(newLesson.slides.length - 1);
    return newLesson;
  };

  const removeSlide = (slideIndex: number) => {
    if (currentLesson.slides.length <= 1) return currentLesson; // Keep at least one slide

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.filter((_, index) => index !== slideIndex),
    };

    const newCurrentIndex =
      slideIndex === currentSlideIndex && slideIndex > 0
        ? slideIndex - 1
        : currentSlideIndex > slideIndex
        ? currentSlideIndex - 1
        : currentSlideIndex;

    setCurrentLesson(newLesson);
    setCurrentSlideIndex(
      Math.min(newCurrentIndex, newLesson.slides.length - 1)
    );
    return newLesson;
  };

  const addSectionToSlide = (template: LayoutTemplate) => {
    const newSection: LayoutSection = {
      id: `section-${Date.now()}`,
      name: `${template.name} Section`,
      template: template,
      zones: template.zones.map((zone) => ({ ...zone, components: [] })),
    };

    const updatedSlide = {
      ...currentSlide,
      sections: [...currentSlide.sections, newSection],
    };

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, index) =>
        index === currentSlideIndex ? updatedSlide : slide
      ),
    };

    setCurrentLesson(newLesson);
    return newLesson;
  };

  const removeSectionFromSlide = (sectionId: string) => {
    const updatedSlide = {
      ...currentSlide,
      sections: currentSlide.sections.filter(
        (section) => section.id !== sectionId
      ),
    };

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, index) =>
        index === currentSlideIndex ? updatedSlide : slide
      ),
    };

    setCurrentLesson(newLesson);
    return newLesson;
  };

  const addComponentToZone = (
    sectionId: string,
    zoneId: string,
    type: LayoutComponent["type"]
  ) => {
    const newComponent = createNewComponent(type);

    const updatedSlide = {
      ...currentSlide,
      sections: currentSlide.sections.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              zones: section.zones.map((zone) =>
                zone.id === zoneId
                  ? { ...zone, components: [...zone.components, newComponent] }
                  : zone
              ),
            }
          : section
      ),
    };

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, index) =>
        index === currentSlideIndex ? updatedSlide : slide
      ),
    };

    setCurrentLesson(newLesson);
    return newLesson;
  };

  const removeComponentFromZone = (
    sectionId: string,
    zoneId: string,
    componentId: string
  ) => {
    const updatedSlide = {
      ...currentSlide,
      sections: currentSlide.sections.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              zones: section.zones.map((zone) =>
                zone.id === zoneId
                  ? {
                      ...zone,
                      components: zone.components.filter(
                        (comp) => comp.id !== componentId
                      ),
                    }
                  : zone
              ),
            }
          : section
      ),
    };

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, index) =>
        index === currentSlideIndex ? updatedSlide : slide
      ),
    };

    setCurrentLesson(newLesson);
    return newLesson;
  };

  const updateComponentContent = (
    sectionId: string,
    zoneId: string,
    componentId: string,
    newContent: string | object
  ) => {
    const updatedSlide = {
      ...currentSlide,
      sections: currentSlide.sections.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              zones: section.zones.map((zone) =>
                zone.id === zoneId
                  ? {
                      ...zone,
                      components: zone.components.map((comp) =>
                        comp.id === componentId
                          ? { ...comp, content: newContent }
                          : comp
                      ),
                    }
                  : zone
              ),
            }
          : section
      ),
    };

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, index) =>
        index === currentSlideIndex ? updatedSlide : slide
      ),
    };

    setCurrentLesson(newLesson);
    return newLesson;
  };

  return {
    currentLesson,
    currentSlide,
    currentSlideIndex,
    setCurrentSlideIndex,
    updateLesson,
    addSlide,
    removeSlide,
    addSectionToSlide,
    removeSectionFromSlide,
    addComponentToZone,
    removeComponentFromZone,
    updateComponentContent,
  };
};
