import RichTextEditor, { BaseKit } from "@aicademy/tiptap-editor";
import { Attachment } from "@aicademy/tiptap-editor/attachment";
import { Blockquote } from "@aicademy/tiptap-editor/blockquote";
import { Bold } from "@aicademy/tiptap-editor/bold";
import {
  BubbleMenuDrawer,
  BubbleMenuExcalidraw,
  BubbleMenuKatex,
  BubbleMenuMermaid,
} from "@aicademy/tiptap-editor/bubble-extra";
import { BulletList } from "@aicademy/tiptap-editor/bulletlist";
import { Clear } from "@aicademy/tiptap-editor/clear";
import { Code } from "@aicademy/tiptap-editor/code";
import { CodeBlock } from "@aicademy/tiptap-editor/codeblock";
import { Color } from "@aicademy/tiptap-editor/color";
import { Emoji } from "@aicademy/tiptap-editor/emoji";
import { ExportPdf } from "@aicademy/tiptap-editor/exportpdf";
import { ExportWord } from "@aicademy/tiptap-editor/exportword";
import { FontSize } from "@aicademy/tiptap-editor/fontsize";
import { FormatPainter } from "@aicademy/tiptap-editor/formatpainter";
import { Heading } from "@aicademy/tiptap-editor/heading";
import { Highlight } from "@aicademy/tiptap-editor/highlight";
import { History } from "@aicademy/tiptap-editor/history";
import { HorizontalRule } from "@aicademy/tiptap-editor/horizontalrule";
import { Image } from "@aicademy/tiptap-editor/image";
import { Indent } from "@aicademy/tiptap-editor/indent";
import { Italic } from "@aicademy/tiptap-editor/italic";
import { Katex } from "@aicademy/tiptap-editor/katex";
import { HighlightBox } from "@aicademy/tiptap-editor/lib/HighlightBox.js";
import { LineHeight } from "@aicademy/tiptap-editor/lineheight";
import { Link } from "@aicademy/tiptap-editor/link";
import { Mention } from "@aicademy/tiptap-editor/mention";
import { MoreMark } from "@aicademy/tiptap-editor/moremark";
import { ColumnActionButton } from "@aicademy/tiptap-editor/multicolumn";
import { OrderedList } from "@aicademy/tiptap-editor/orderedlist";
import { SearchAndReplace } from "@aicademy/tiptap-editor/searchandreplace";
import { SlashCommand } from "@aicademy/tiptap-editor/slashcommand";
import { Strike } from "@aicademy/tiptap-editor/strike";
import { Table } from "@aicademy/tiptap-editor/table";
import { TableOfContents } from "@aicademy/tiptap-editor/tableofcontent";
import { TaskList } from "@aicademy/tiptap-editor/tasklist";
import { TextAlign } from "@aicademy/tiptap-editor/textalign";

import { TextUnderline } from "@aicademy/tiptap-editor/textunderline";
import { useCallback, useState } from "react";

import "@aicademy/tiptap-editor/lib/style.css";
import { UploadFile } from "@/services/instructor";

const extensions = [
  BaseKit.configure({
    placeholder: {
      showOnlyCurrent: true,
    },
    characterCount: {
      limit: 50_000,
    },
  }),
  History,
  SearchAndReplace,
  TableOfContents,
  FormatPainter.configure({ spacer: true }),
  Clear,
  Heading.configure({ spacer: true }),
  FontSize,
  Bold,
  Italic,
  TextUnderline,
  Strike,
  MoreMark,
  Emoji,
  Color.configure({ spacer: true }),
  Highlight,
  BulletList,
  OrderedList,
  TextAlign.configure({ types: ["heading", "paragraph"], spacer: true }),
  Indent,
  LineHeight,
  TaskList.configure({
    spacer: true,
    taskItem: {
      nested: true,
    },
  }),
  Link,
  Image.configure({
    upload: (files: File) => {
      return UploadFile({ file: files });
    },
    HTMLAttributes: {
      style: "max-width: 100%;",
    },
  }),
  Blockquote,
  SlashCommand,
  HorizontalRule,
  Code.configure({
    toolbar: false,
  }),
  CodeBlock,
  ColumnActionButton,
  Table,
  ExportPdf.configure({ spacer: true }),
  ExportWord,
  Mention,
  Attachment.configure({
    upload: (file: any) => {
      return UploadFile({ file: file });
    },
  }),
  Katex,
  HighlightBox.configure({ spacer: true }),
];

function debounce(func: any, wait: number) {
  let timeout: NodeJS.Timeout;
  return function (...args: any[]) {
    clearTimeout(timeout);
    // @ts-ignore
    timeout = setTimeout(() => func.apply(this, args), wait);
  };
}

interface BeautyEditorProps {
  content: string;
  setContent: (content: string) => void;
  contentId: string;
}

function BeautyEditor({ content, setContent, contentId }: BeautyEditorProps) {
  const [theme, setTheme] = useState("light");
  const [disable, setDisable] = useState(false);

  const onValueChange = useCallback(
    debounce((value: any) => {
      // Ensure we always pass a JSON string to setContent
      try {
        const jsonString =
          typeof value === "string" ? value : JSON.stringify(value);
        setContent(jsonString);
      } catch (error) {
        console.error("Error converting content to JSON:", error);
        setContent("{}");
      }
    }, 500),
    [],
  );

  // Parse content safely
  let parsedContent;
  try {
    parsedContent = JSON.parse(content);
  } catch (error) {
    console.error("Error parsing content:", error);
    parsedContent = { type: "doc", content: [] };
  }

  return (
    <div className="w-full">
      <RichTextEditor
        output="json"
        content={parsedContent}
        onChangeContent={onValueChange}
        extensions={extensions}
        contentId={contentId}
        dark={theme === "dark"}
        contentClass={"ms-4"}
        disabled={disable}
        // bubbleMenu={{
        //   render({ extensionsNames, editor, disabled }, bubbleDefaultDom) {
        //     return (
        //       <>
        //         {bubbleDefaultDom}
        //         {extensionsNames.includes("katex") ? (
        //           <BubbleMenuKatex
        //             disabled={disabled}
        //             editor={editor}
        //             key="katex"
        //           />
        //         ) : null}
        //         {extensionsNames.includes("excalidraw") ? (
        //           <BubbleMenuExcalidraw
        //             disabled={disabled}
        //             editor={editor}
        //             key="excalidraw"
        //           />
        //         ) : null}
        //         {extensionsNames.includes("mermaid") ? (
        //           <BubbleMenuMermaid
        //             disabled={disabled}
        //             editor={editor}
        //             key="mermaid"
        //           />
        //         ) : null}
        //         {extensionsNames.includes("drawer") ? (
        //           <BubbleMenuDrawer
        //             disabled={disabled}
        //             editor={editor}
        //             key="drawer"
        //           />
        //         ) : null}
        //       </>
        //     );
        //   },
        // }}
      />
    </div>
  );
}

export default BeautyEditor;
