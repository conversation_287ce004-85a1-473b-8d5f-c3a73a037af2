import { createFileRoute } from "@tanstack/react-router";
import logoImg from "@/assets/images/aicademy-logo.png";
import { LoginMenu } from "@/components/auth";

export const Route = createFileRoute("/auth")({
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-100 dark:from-gray-900 dark:via-gray-800 dark:to-indigo-900">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="w-full max-w-md">
          {/* Background Card */}
          <div className="space-y-6 rounded-2xl border border-white/20 bg-white/80 p-8 shadow-2xl backdrop-blur-lg">
            {/* Header */}
            <div className="space-y-2 text-center">
              <img src={logoImg} alt="" />
              <h2 className="text-center font-medium text-2xl">Đ<PERSON>ng nhập</h2>
            </div>

            {/* Login Form */}
            <LoginMenu />
          </div>

          {/* Footer */}
          <p className="mt-8 text-center text-gray-500 text-sm">
            © 2024 AiCademy. Tất cả quyền được bảo lưu.
          </p>
        </div>
      </div>
    </div>
  );
}
