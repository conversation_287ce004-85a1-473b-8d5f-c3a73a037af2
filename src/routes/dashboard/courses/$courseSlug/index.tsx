import { createFileRoute } from "@tanstack/react-router";
import { Bar<PERSON>hart3, BookOpen, History, TrendingUp, Users } from "lucide-react";
import { useState } from "react";
import { CourseContent } from "@/components/course-detail/course-content";

// Import refactored components
import { CourseHeader } from "@/components/course-detail/course-header";
import { CourseHistory } from "@/components/course-detail/course-history";
import { CourseOverview } from "@/components/course-detail/course-overview";
import { CoursePermissions } from "@/components/course-detail/course-permissions";
import { CourseStats } from "@/components/course-detail/course-stats";
import { StudentsTable } from "@/components/course-detail/students-table";
import type {
  CourseData,
  Department,
  HistoryItem,
  Lesson,
  Permission,
  Student,
  User,
} from "@/components/course-detail/types";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export const Route = createFileRoute("/dashboard/courses/$courseSlug/")({
  loader: async ({ params: { courseSlug } }) => {
    return { courseSlug };
  },
  component: RouteComponent,
});

function RouteComponent() {
  const { courseSlug } = Route.useLoaderData();

  const generateCourseData = (slug: string): CourseData => {
    const courses = [
      "React Fundamentals",
      "Advanced JavaScript",
      "TypeScript Mastery",
      "UI/UX Design Basics",
      "Node.js Backend",
    ];

    const categories = ["Development", "Design", "Marketing", "Data Science"];
    const levels = ["Beginner", "Intermediate", "Advanced"];
    const instructors = [
      "Nguyễn Văn A",
      "Trần Thị B",
      "Lê Minh C",
      "Phạm Thị D",
    ];
    const requirements = ["company", "department", "optional"] as const;

    const id = Math.floor(Math.random() * 100) + 1;
    const courseIndex = Math.floor(Math.random() * courses.length);
    const course = courses[courseIndex] || "React Fundamentals";

    return {
      id,
      title: course,
      slug: course.toLowerCase().replace(/\s+/g, "-"),
      description: `Học ${course} từ cơ bản đến nâng cao với các ví dụ thực tế`,
      category: categories[courseIndex % categories.length],
      level: levels[courseIndex % levels.length],
      goal: `Sau khi hoàn thành khóa học này, bạn sẽ có thể áp dụng ${course} vào các dự án thực tế`,
      imageUrl:
        "https://pikaso.cdnpk.net/private/production/2156139939/render.png?token=exp=1774483200~hmac=5cfd6026c36274d4ce044ba0303e789d35c3b84b330d0eb3455de8adbcb11b97",
      status: courseIndex % 3 === 0 ? "draft" : "published",
      duration: `${4 + (courseIndex % 8)} tuần`,
      lessons: 8 + (courseIndex % 15),
      enrolled: 50 + ((courseIndex * 47) % 300),
      completed: 20 + ((courseIndex * 23) % 150),
      createdAt: `2024-0${1 + (courseIndex % 2)}-${10 + (courseIndex % 20)}`,
      updatedAt: `2024-0${1 + (courseIndex % 2)}-${15 + (courseIndex % 15)}`,
      instructor: instructors[courseIndex % instructors.length],
      completionRate: 65 + ((courseIndex * 7) % 35),
      averageScore: 75 + ((courseIndex * 5) % 25),
      requirement: requirements[courseIndex % requirements.length],
    };
  };

  const [courseData, setCourseData] = useState<CourseData>(
    generateCourseData(courseSlug),
  );

  const [isPublishing, setIsPublishing] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  const lessons: Lesson[] = [
    {
      id: 1,
      title: "Giới thiệu về React",
      type: "Video",
      duration: "15 phút",
      status: "Đã xuất bản",
      module: 1,
      slug: "gioi-thieu-ve-react",
      sections: 3,
      contentType: "video",
    },
    {
      id: 2,
      title: "Components và Props",
      type: "Video",
      duration: "20 phút",
      status: "Đã xuất bản",
      module: 1,
      slug: "components-va-props",
      sections: 4,
      contentType: "text",
    },
    {
      id: 3,
      title: "State và Lifecycle",
      type: "Văn bản",
      duration: "10 phút",
      status: "Đã xuất bản",
      module: 2,
      slug: "state-va-lifecycle",
      sections: 2,
      contentType: "quiz",
    },
    {
      id: 4,
      title: "Bài tập thực hành 1",
      type: "Bài tập",
      duration: "30 phút",
      status: "Bản nháp",
      module: 2,
      slug: "bai-tap-thuc-hanh-1",
      sections: 5,
      contentType: "game",
    },
  ];

  const courseHistory: HistoryItem[] = [
    {
      id: 1,
      action: "Tạo khóa học",
      type: "create",
      description: "Khóa học React Fundamentals đã được tạo mới",
      user: "Jane Smith",
      role: "Instructor",
      date: "2024-01-10",
      time: "10:00 AM",
    },
    {
      id: 2,
      action: "Chỉnh sửa nội dung",
      type: "edit",
      description: "Đã cập nhật nội dung bài học Giới thiệu về React",
      user: "John Doe",
      role: "Editor",
      date: "2024-01-12",
      time: "02:30 PM",
    },
    {
      id: 3,
      action: "Cập nhật phân quyền",
      type: "permission",
      description: "Đã cấp quyền truy cập cho phòng ban Kỹ thuật",
      user: "Alice Johnson",
      role: "Admin",
      date: "2024-01-15",
      time: "09:45 AM",
    },
  ];

  const departmentPermissions: Permission[] = [
    { id: 1, department: "Kỹ thuật", hasAccess: true, enrolledCount: 45 },
    { id: 2, department: "Marketing", hasAccess: true, enrolledCount: 23 },
    { id: 3, department: "Nhân sự", hasAccess: false, enrolledCount: 0 },
    { id: 4, department: "Bán hàng", hasAccess: true, enrolledCount: 12 },
    { id: 5, department: "Tài chính", hasAccess: false, enrolledCount: 0 },
  ];

  const individualPermissions: Permission[] = [
    {
      id: 1,
      name: "Nguyễn Văn A",
      email: "<EMAIL>",
      department: "Kỹ thuật",
      hasAccess: true,
    },
    {
      id: 2,
      name: "Trần Thị B",
      email: "<EMAIL>",
      department: "Marketing",
      hasAccess: true,
    },
    {
      id: 3,
      name: "Phạm Thị D",
      email: "<EMAIL>",
      department: "Bán hàng",
      hasAccess: true,
    },
  ];

  const departments: Department[] = [
    { id: 1, name: "Kỹ thuật", userCount: 45 },
    { id: 2, name: "Marketing", userCount: 23 },
    { id: 3, name: "Nhân sự", userCount: 15 },
    { id: 4, name: "Bán hàng", userCount: 12 },
    { id: 5, name: "Tài chính", userCount: 8 },
  ];

  const users: User[] = [
    {
      id: 1,
      name: "Nguyễn Văn A",
      email: "<EMAIL>",
      department: "Kỹ thuật",
    },
    {
      id: 2,
      name: "Trần Thị B",
      email: "<EMAIL>",
      department: "Marketing",
    },
    {
      id: 3,
      name: "Lê Minh C",
      email: "<EMAIL>",
      department: "Nhân sự",
    },
    {
      id: 4,
      name: "Phạm Thị D",
      email: "<EMAIL>",
      department: "Bán hàng",
    },
  ];

  const generateStudents = (): Student[] => {
    const names = [
      "Nguyễn Văn A",
      "Trần Thị B",
      "Lê Minh C",
      "Phạm Thu D",
      "Hoàng Văn E",
      "Vũ Thị F",
      "Đặng Minh G",
      "Bùi Thu H",
      "Ngô Văn I",
      "Lý Thị K",
    ];
    const departments = [
      "Kỹ thuật",
      "Marketing",
      "Nhân sự",
      "Bán hàng",
      "Tài chính",
    ];
    const statuses = ["Hoàn thành", "Đang học", "Tạm dừng"];

    return Array.from({ length: 50 }, (_, i) => ({
      id: i + 1,
      name: names[i % names.length],
      department: departments[i % departments.length],
      enrolledDate: `2024-03-${String((i % 28) + 1).padStart(2, "0")}`,
      progress: Math.floor(Math.random() * 100),
      status: statuses[i % statuses.length],
      score: Math.floor(Math.random() * 40) + 60,
      studyTime: `${Math.floor(Math.random() * 20) + 1} giờ`,
      lastAccess: `${Math.floor(Math.random() * 7) + 1} ${
        Math.random() > 0.5 ? "ngày" : "giờ"
      } trước`,
      role: "Developer",
    }));
  };

  const studentsData = generateStudents();

  const handleSave = (updatedData: Partial<CourseData>) => {
    setCourseData({ ...courseData, ...updatedData });
  };

  const handleBack = () => {
    window.history.back();
  };

  const handlePublish = () => {
    setIsPublishing(true);
    setTimeout(() => {
      setCourseData({ ...courseData, status: "published" });
      setIsPublishing(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <CourseHeader
        courseData={courseData}
        isPublishing={isPublishing}
        onBack={handleBack}
        onPublish={handlePublish}
      />
      <main className="flex-1 px-4 py-6 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            {/* Mobile dropdown for tabs */}
            <div className="mb-6 block sm:hidden">
              <select
                value={activeTab}
                onChange={(e) => setActiveTab(e.target.value)}
                className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 font-medium text-sm shadow-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              >
                <option value="overview">📈 Tổng quan</option>
                <option value="stats">📊 Thống kê</option>
                <option value="content">📚 Nội dung</option>
                <option value="permissions">👥 Phân quyền</option>
                <option value="history">🕒 Lịch sử</option>
              </select>
            </div>

            {/* Desktop tabs */}
            <TabsList className="!pb-2 hidden w-full rounded-xl px-1 shadow-sm sm:grid sm:grid-cols-3 lg:grid-cols-5">
              <TabsTrigger
                value="overview"
                className="flex items-center gap-1 text-xs transition-all duration-200 sm:gap-2 sm:text-sm"
              >
                <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden sm:inline">Tổng quan</span>
                <span className="sm:hidden">TQ</span>
              </TabsTrigger>
              <TabsTrigger
                value="stats"
                className="flex items-center gap-1 text-xs transition-all duration-200 sm:gap-2 sm:text-sm"
              >
                <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden sm:inline">Thống kê</span>
                <span className="sm:hidden">TK</span>
              </TabsTrigger>
              <TabsTrigger
                value="content"
                className="flex items-center gap-1 text-xs transition-all duration-200 sm:gap-2 sm:text-sm"
              >
                <BookOpen className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden sm:inline">Nội dung</span>
                <span className="sm:hidden">ND</span>
              </TabsTrigger>
              <TabsTrigger
                value="permissions"
                className="flex items-center gap-1 text-xs transition-all duration-200 sm:gap-2 sm:text-sm lg:col-span-1"
              >
                <Users className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden sm:inline">Phân quyền</span>
                <span className="sm:hidden">PQ</span>
              </TabsTrigger>
              <TabsTrigger
                value="history"
                className="flex items-center gap-1 text-xs transition-all duration-200 sm:gap-2 sm:text-sm lg:col-span-1"
              >
                <History className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden sm:inline">Lịch sử</span>
                <span className="sm:hidden">LS</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-4 sm:mt-6">
              <div className="space-y-4 sm:space-y-6">
                <CourseOverview courseData={courseData} onSave={handleSave} />
              </div>
            </TabsContent>

            <TabsContent
              value="stats"
              className="mt-4 space-y-4 sm:mt-6 sm:space-y-6"
            >
              <CourseStats courseData={courseData} />
              <div className="overflow-hidden">
                <StudentsTable students={studentsData} />
              </div>
            </TabsContent>

            <TabsContent value="content" className="mt-4 sm:mt-6">
              <div className="space-y-4 sm:space-y-6">
                <CourseContent lessons={lessons} />
              </div>
            </TabsContent>

            <TabsContent value="permissions" className="mt-4 sm:mt-6">
              <div className="space-y-4 sm:space-y-6">
                <CoursePermissions
                  departmentPermissions={departmentPermissions}
                  individualPermissions={individualPermissions}
                  departments={departments}
                  users={users}
                />
              </div>
            </TabsContent>

            <TabsContent value="history" className="mt-4 sm:mt-6">
              <div className="space-y-4 sm:space-y-6">
                <CourseHistory history={courseHistory} />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}
