import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import { useCourseFilters } from "@/components/courses/hooks/useCourseFilters";
import { usePagination } from "@/components/courses/hooks/usePagination";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { SortableTableHead } from "@/components/ui/sortable-header";
import { Table, TableBody, TableHeader, TableRow } from "@/components/ui/table";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useSortableTable } from "@/hooks/use-table-sorting";
import { CourseFilters } from "../../../components/courses/CourseFilters";
import { CourseTableRow } from "../../../components/courses/CourseTableRow";
import { CreateCourseDialog } from "../../../components/courses/CreateCourseDialog";
import { Pagination } from "../../../components/courses/Pagination";
import { ITEMS_PER_PAGE_OPTIONS, MOCK_COURSES } from "../../../utils/constants";

export const Route = createFileRoute("/dashboard/courses/")({
  component: RouteComponent,
});

function RouteComponent() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const navigate = Route.useNavigate();

  const { filters, filteredCourses, updateFilter } =
    useCourseFilters(MOCK_COURSES);

  const {
    sortedData: sortedCourses,
    handleSort,
    getSortIcon,
  } = useSortableTable({
    data: filteredCourses,
    defaultSort: { column: "title", direction: "asc" },
  });

  const {
    currentItems: currentCourses,
    pagination,
    totalPages,
    startIndex,
    endIndex,
    setCurrentPage,
    setItemsPerPage,
    resetToFirstPage,
  } = usePagination(sortedCourses, 10);

  const handleFilterChange = (type: keyof typeof filters, value: string) => {
    resetToFirstPage();
    updateFilter(type, value);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "published":
        return "bg-black text-white";
      case "draft":
        return "bg-blue-100 text-blue-800";
      case "archived":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "published":
        return "Đã xuất bản";
      case "draft":
        return "Bản nháp";
      case "archived":
        return "Đã lưu trữ";
      default:
        return status;
    }
  };

  const getRequirementColor = (requirement: string) => {
    switch (requirement) {
      case "company":
        return "bg-red-100 text-red-800 border-red-200";
      case "department":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "optional":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getRequirementText = (requirement: string) => {
    switch (requirement) {
      case "company":
        return "Bắt buộc cả công ty";
      case "department":
        return "Bắt buộc theo phòng ban";
      case "optional":
        return "Tùy chọn/khuyến khích";
      default:
        return requirement;
    }
  };

  const handleViewCourse = (courseId: number) => {
    navigate({
      to: "/dashboard/courses/$courseSlug",
      params: { courseSlug: courseId.toString() },
    });
  };

  return (
    <div className="flex h-screen bg-background">
      <div className="flex flex-1 flex-col overflow-hidden">
        <div className="border-b bg-background px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="font-bold text-2xl text-foreground">
                Quản lý khóa học
              </h1>
              <p className="text-muted-foreground">
                Quản lý tất cả khóa học trong một nơi
              </p>
            </div>
            <CreateCourseDialog
              isOpen={isCreateDialogOpen}
              onOpenChange={setIsCreateDialogOpen}
            />
          </div>
        </div>

        <main className="flex-1 overflow-y-auto p-6">
          <div className="space-y-6">
            <CourseFilters
              filters={filters}
              onFilterChange={handleFilterChange}
            />

            {/* Courses Table */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>
                    Danh sách khóa học ({sortedCourses.length})
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground text-sm">
                      Hiển thị:
                    </span>
                    <select
                      value={pagination.itemsPerPage.toString()}
                      onChange={(e) => setItemsPerPage(Number(e.target.value))}
                      className="rounded border px-2 py-1 text-sm"
                    >
                      {ITEMS_PER_PAGE_OPTIONS.map((option) => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                    <span className="text-muted-foreground text-sm">mục</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <TooltipProvider>
                  <div className="max-h-[600px] overflow-auto">
                    <Table>
                      <TableHeader className="sticky top-0 z-10 bg-white">
                        <TableRow>
                          <SortableTableHead
                            sortDirection={getSortIcon("title")}
                            onSort={() => handleSort("title")}
                          >
                            Tên khóa học
                          </SortableTableHead>
                          <SortableTableHead
                            sortDirection={getSortIcon("instructor")}
                            onSort={() => handleSort("instructor")}
                          >
                            Giảng viên
                          </SortableTableHead>
                          <SortableTableHead
                            sortDirection={getSortIcon("creationDate")}
                            onSort={() => handleSort("creationDate")}
                          >
                            Ngày tạo
                          </SortableTableHead>
                          <SortableTableHead
                            sortDirection={getSortIcon("status")}
                            onSort={() => handleSort("status")}
                          >
                            Trạng thái
                          </SortableTableHead>
                          <SortableTableHead
                            sortDirection={getSortIcon("requirement")}
                            onSort={() => handleSort("requirement")}
                          >
                            Loại khóa học
                          </SortableTableHead>
                          <SortableTableHead
                            sortDirection={getSortIcon("students")}
                            onSort={() => handleSort("students")}
                          >
                            Học viên
                          </SortableTableHead>
                          <SortableTableHead
                            sortDirection="none"
                            sortable={false}
                          >
                            Thao tác
                          </SortableTableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {currentCourses.map((course) => (
                          <CourseTableRow
                            key={course.id}
                            course={course}
                            onViewCourse={handleViewCourse}
                            getStatusColor={getStatusColor}
                            getStatusText={getStatusText}
                            getRequirementColor={getRequirementColor}
                            getRequirementText={getRequirementText}
                          />
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </TooltipProvider>

                {totalPages > 1 && (
                  <div className="mt-6 flex items-center justify-between">
                    <div className="text-muted-foreground text-sm">
                      Results: {startIndex + 1} -{" "}
                      {Math.min(endIndex, sortedCourses.length)} of{" "}
                      {sortedCourses.length}
                    </div>
                    <Pagination
                      currentPage={pagination.currentPage}
                      totalPages={totalPages}
                      onPageChange={setCurrentPage}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
