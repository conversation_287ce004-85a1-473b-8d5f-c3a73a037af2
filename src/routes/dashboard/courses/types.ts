export interface Course {
  id: number;
  title: string;
  description: string;
  category: string;
  status: "published" | "draft" | "archived";
  completion: number;
  students: number;
  revenue: number;
  lastUpdated: string;
  instructor: string;
  createdBy: string;
  creationDate: string;
  tags: string[];
  completionRate: number;
  averageScore: number;
  totalRatings: number;
  averageRating: number;
  department: string;
  requirement: "company" | "department" | "optional";
  [key: string]: string | number | boolean | Date | string[] | null | undefined;
}

export interface CourseFilters {
  searchTerm: string;
  status: string;
  department: string;
  instructor: string;
  sortBy: string;
}

export interface PaginationState {
  currentPage: number;
  itemsPerPage: number;
}
