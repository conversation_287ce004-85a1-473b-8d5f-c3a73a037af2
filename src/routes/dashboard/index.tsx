import { createFileRoute, redirect } from "@tanstack/react-router";
import { SiteHeader } from "@/components/site-header";

export const Route = createFileRoute("/dashboard/")({
  beforeLoad: ({ params, location }) => {
    if (["/dashboard", "/dashboard/"].includes(location.pathname)) {
      throw redirect({ to: "/dashboard/analytics" });
    }
  },
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <div>
      <SiteHeader />
    </div>
  );
}
