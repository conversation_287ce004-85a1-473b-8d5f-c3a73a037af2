import { queryOptions, useQuery } from "@tanstack/react-query";
import { UUID } from "crypto";
import { useAtomValue } from "jotai";
import { atomAuth } from "@/store/auth";
import type { Course, UserCourse } from "@/types/courses";
import { RatingValues, UserVoting } from "@/types/rating";
import { API_URL, req } from "@/utils/api";
import { qs } from "@/utils/qs";

export type FetchCoursesParams = {
  category_id?: string;
  stats?: boolean;
};

export const QUERY_KEY_COURSE = {
  base: ["courses"] as const,
  list: (params?: FetchCoursesParams) =>
    [...QUERY_KEY_COURSE.base, params] as const,
  detail: (id: string) => [...QUERY_KEY_COURSE.base, id] as const,
  progresses: (params: [UUID, string]) =>
    [...QUERY_KEY_COURSE.base, "progresses", ...params] as const,
  voting: () => [...QUERY_KEY_COURSE.base, "voting"] as const,
};

export const fetchCourses = ({ data }: { data?: FetchCoursesParams }) => {
  return req<Course[]>(
    `${API_URL}/api/v1/courses${qs({
      stats: true,
      page: 1,
      limit: 20,
      category_id: data?.category_id,
    })}`,
  ).then((res) =>
    res
      .map((course) => ({
        ...course,
        unpublished: course.published_at > new Date().toISOString(),
      }))
      .sort((a, b) =>
        a.unpublished === b.unpublished ? 0 : a.unpublished ? 1 : -1,
      ),
  );
};

export function useCourses(data?: FetchCoursesParams) {
  return useQuery(coursesQueryOptions(data));
}

export const coursesQueryOptions = (data?: FetchCoursesParams) =>
  queryOptions({
    queryKey: QUERY_KEY_COURSE.list(data),
    queryFn: () => fetchCourses({ data }),
    staleTime: 60 * 1000,
  });

export const fetchCourse = ({ data }: { data: string }) => {
  return req<Course>(`${API_URL}/api/v1/courses/${data}`).then((res) => ({
    ...res,
    unpublished: res.published_at > new Date().toISOString(),
  }));
};

export const courseQueryOptions = (courseSlug: string) =>
  queryOptions({
    queryKey: QUERY_KEY_COURSE.detail(courseSlug),
    queryFn: () => fetchCourse({ data: courseSlug }),
    staleTime: 60 * 1000,
  });

export const useCourse = (courseSlug: string) => {
  return useQuery(courseQueryOptions(courseSlug));
};

export const fetchCourseProgresses = (courseId: string) => {
  return req<UserCourse>(`${API_URL}/api/v1/courses/${courseId}/progress`, {
    withAuth: true,
  });
};

export const courseProgressesQueryOptions = (
  id: UUID,
  authJwt: string | undefined,
) => {
  return queryOptions({
    queryKey: QUERY_KEY_COURSE.progresses([id, authJwt || ""]),
    queryFn: () => fetchCourseProgresses(id),
    retry: false,
    enabled: !!authJwt,
  });
};

export const useUserCourseProgresses = (id: UUID) => {
  const authJwt = useAtomValue(atomAuth)?.jwt;
  return useQuery(courseProgressesQueryOptions(id, authJwt));
};

export const voteCourse = async (
  course_id: string,
  ratingValues: RatingValues & { comment: string },
) => {
  return req(`${API_URL}/api/v1/courses/${course_id}/vote`, {
    method: "POST",
    withAuth: true,
    body: JSON.stringify(ratingValues),
  });
};

export const revoteCourse = async (
  course_id: string,
  ratingValues: RatingValues & { comment: string },
) => {
  return req(`${API_URL}/api/v1/courses/${course_id}/vote`, {
    method: "PATCH",
    withAuth: true,
    body: JSON.stringify(ratingValues),
  });
};

export const fetchCourseVote = async (course_id: string) => {
  return req<UserVoting[]>(`${API_URL}/api/v1/courses/${course_id}/voting`);
};
