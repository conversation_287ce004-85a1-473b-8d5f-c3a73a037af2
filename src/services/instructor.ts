import { queryOptions, UseQueryResult, useQuery } from "@tanstack/react-query";
import { Category } from "@/types/app";
import { Course } from "@/types/courses";
import { Lesson, Section } from "@/types/lessons";
import { Quiz } from "@/types/quizzes";
import { API_URL, APIResult, AppError, getAuthToken, req } from "@/utils/api";

export interface CodegenResponse {
  message: string;
  data: {
    react_code: string;
  };
}

export function updateSection(params: {
  id: string;
  section: Section;
}): Promise<APIResult<Section>> {
  return req<APIResult<Section>>(
    `${API_URL}/api/v1/instructors/sections/${params.id}`,
    {
      method: "PATCH",
      withAuth: true,
      headers: {
        "content-type": "application/json",
      },
      body: JSON.stringify(params.section),
    },
  ).then((r) => r);
}

export function createSection(params: {
  section: Section;
  categories: string[];
}): Promise<APIResult<Section>> {
  return req<APIResult<Section>>(`${API_URL}/api/v1/instructors/sections`, {
    method: "POST",
    withAuth: true,
    headers: {
      "content-type": "application/json",
    },
    body: JSON.stringify(params.section),
  }).then((r) => r);
}

export function createCourse(params: {
  course: Course;
  categories: string[];
}): Promise<APIResult<Course>> {
  return req<APIResult<Course>>(`${API_URL}/api/v1/instructors/courses`, {
    method: "POST",
    withAuth: true,
    headers: {
      "content-type": "application/json",
    },
    body: JSON.stringify({
      course: params.course,
      categories: params.categories,
    }),
  }).then((r) => r);
}

export function updateCourse(params: {
  id: string;
  course: Course;
  categories: string[];
}): Promise<APIResult<Course>> {
  return req<APIResult<Course>>(
    `${API_URL}/api/v1/instructors/courses/${params.id}`,
    {
      method: "PATCH",
      withAuth: true,
      headers: {
        "content-type": "application/json",
      },
      body: JSON.stringify({
        course: params.course,
        categories: params.categories,
      }),
    },
  ).then((r) => r);
}
export function createLesson(params: {
  lesson: Lesson;
}): Promise<APIResult<Course>> {
  return req<APIResult<Course>>(`${API_URL}/api/v1/instructors/lessons`, {
    method: "POST",
    withAuth: true,
    headers: {
      "content-type": "application/json",
    },
    body: JSON.stringify(params.lesson),
  }).then((r) => r);
}

export function updateLesson(params: {
  id: string;
  lesson: Lesson;
}): Promise<APIResult<Section>> {
  return req<APIResult<Section>>(
    `${API_URL}/api/v1/instructors/lessons/${params.id}`,
    {
      method: "PATCH",
      withAuth: true,
      headers: {
        "content-type": "application/json",
      },
      body: JSON.stringify(params.lesson),
    },
  ).then((r) => r);
}

export function DeleteSection(params: {
  id: string;
}): Promise<APIResult<Section>> {
  return req<APIResult<Section>>(
    `${API_URL}/api/v1/instructors/sections/${params.id}`,
    {
      method: "DELETE",
      withAuth: true,
      headers: {
        "content-type": "application/json",
      },
    },
  ).then((r) => r);
}
export function createQuiz(params: { quiz: Quiz }): Promise<APIResult<Quiz>> {
  return req<APIResult<Quiz>>(`${API_URL}/api/v1/instructors/quizzes`, {
    method: "POST",
    withAuth: true,
    headers: {
      "content-type": "application/json",
    },
    body: JSON.stringify(params.quiz),
  }).then((r) => r);
}
export function updateQuiz(params: {
  id: string;
  quiz: Quiz;
}): Promise<APIResult<Quiz>> {
  return req<APIResult<Quiz>>(
    `${API_URL}/api/v1/instructors/quizzes/${params.id}`,
    {
      method: "PATCH",
      withAuth: true,
      headers: {
        "content-type": "application/json",
      },
      body: JSON.stringify(params.quiz),
    },
  ).then((r) => r);
}
export function DeleteQuiz(params: {
  id: string;
}): Promise<APIResult<Section>> {
  return req<APIResult<Section>>(
    `${API_URL}/api/v1/instructors/quizzes/${params.id}`,
    {
      method: "DELETE",
      withAuth: true,
      headers: {
        "content-type": "application/json",
      },
    },
  ).then((r) => r);
}

export function useQuizzes(
  sectionId: string,
): UseQueryResult<APIResult<Quiz[]>, Error> {
  return useQuery({
    queryKey: ["quizzes", sectionId],
    queryFn: () => fetchQuizzes({ section_id: sectionId }),
  });
}

export function fetchQuizzes(params: {
  section_id: string;
}): Promise<APIResult<Quiz[]>> {
  return req<APIResult<Quiz[]>>(
    `${API_URL}/api/v1/sections/${params.section_id}/quizzes`,
    {
      withAuth: true,
      headers: {
        "content-type": "application/json",
      },
    },
  ).then((r) => r);
}

export function useSections(
  lessonSlug: string,
): UseQueryResult<APIResult<Section[]>, Error> {
  return useQuery({
    queryKey: ["sections", lessonSlug],
    queryFn: () => fetchSections({ lesson_slug: lessonSlug }),
  });
}

export function fetchSections(params: {
  lesson_slug: string;
}): Promise<APIResult<Section[]>> {
  return req<APIResult<Section[]>>(
    `${API_URL}/api/v1/instructors/lessons/${params.lesson_slug}/sections`,
    {
      withAuth: true,
    },
  ).then((r) => r);
}

export const useCourses = (isPublished) => {
  return queryOptions({
    queryKey: ["courses", isPublished],
    queryFn: () => fetchCourses({ published: isPublished }),
  });
};

export function fetchCourses(params: {
  published: boolean;
}): Promise<APIResult<Course[]>> {
  return req<Course[]>(
    `${API_URL}/api/v1/instructors/courses${
      params.published ? "" : "?unpublished"
    }`,
    {
      withAuth: true,
    },
  ).then((data) => data);
}

export const useLessons = (params: { course_id: string }) =>
  queryOptions({
    queryKey: ["lessons", params],
    queryFn: () => fetchLessons(params),
  });

export function fetchLessons(params: {
  course_id: string;
}): Promise<APIResult<Lesson[]>> {
  return req<Lesson[]>(
    `${API_URL}/api/v1/instructors/courses/${params.course_id}/lessons?include_unpublished`,
    {
      withAuth: true,
    },
  ).then((data) => data);
}

export const useLesson = (slug: string) =>
  queryOptions({
    queryKey: ["lesson", slug],
    queryFn: () => fetchLesson(slug),
  });

export function fetchLesson(slug: string): Promise<APIResult<Lesson>> {
  return req<Lesson>(`${API_URL}/api/v1/lessons/${slug}`, {
    headers: {
      "content-type": "application/json",
    },
  }).then((r) => r);
}

export const useCourse = (slug: string) =>
  queryOptions({
    queryKey: ["course", slug],
    queryFn: () => fetchCourse(slug),
  });

export function fetchCourse(slug: string): Promise<APIResult<Course>> {
  return req<APIResult<Course>>(`${API_URL}/api/v1/courses/${slug}`).then(
    (data) => data,
  );
}
export const useCategories = () =>
  queryOptions({
    queryKey: ["categories"],
    queryFn: () => fetchCategories(),
  });

export function fetchCategories(): Promise<APIResult<Category[]>> {
  return req<APIResult<Category[]>>(`${API_URL}/api/v1/categories`).then(
    (r) => r,
  );
}

export function UploadFile(params: { file: File }): Promise<string> {
  const formData = new FormData();
  formData.append("file", params.file);
  return req<{ file_name: string }>(
    `https://worker-files.aicademy.org/api/v1/files`,
    {
      method: "POST",
      withAuth: true,
      body: formData,
    },
  ).then((r) => `https://files.aicademy.org/${r.file_name}`);
}

export function UploadJSXFile(params: {
  jsxCode: string;
}): Promise<APIResult<string>> {
  const formData = new FormData();
  formData.append(
    "file",
    new File([params.jsxCode], "jsx-file-content.jsx", { type: "text/plain" }),
  );
  return req<APIResult<string>>(
    `https://worker-files.aicademy.org/api/v1/files`,
    {
      method: "POST",
      withAuth: true,
      body: formData,
    },
  ).then((r) => `https://files.aicademy.org/${r?.file_name}`);
}

export function GenerateCode(params: {
  message: string;
  model: string;
}): Promise<CodegenResponse> {
  const token = getAuthToken();

  if (!token) {
    return Promise.reject(new AppError("Authn", "No auth token available"));
  }

  return fetch(`https://chat-beta.aicademy.org/api/v1/generate-code/react`, {
    method: "POST",
    headers: {
      "content-type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({
      user_message: params.message,
      gemini_model: params.model,
    }),
  }).then((rs) => rs.json());
}

export function fetchJsxCode(params: { url: string }): Promise<string> {
  return fetch(params.url, {
    method: "GET",
  }).then((rs) => rs.text());
}
