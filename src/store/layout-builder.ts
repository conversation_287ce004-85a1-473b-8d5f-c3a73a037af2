import { atom } from "jotai";
import type { DataMigrationOption } from "../components/lesson-editor/components/LayoutChangeDialog";
import type {
  DraggedComponent,
  DragOverZone,
  LayoutComponent,
  LayoutSection,
  LayoutTemplate,
  Lesson,
  PickerPosition,
  PreviewMode,
  SelectedZone,
} from "../components/lesson-editor/types";
import {
  createDefaultSlide,
  createNewComponent,
} from "../components/lesson-editor/utils";

// Core lesson data atoms (SHARED STATE - used across many components)
export const lessonAtom = atom<Lesson>({
  id: "lesson-1",
  title: "New Lesson",
  description: "Create your lesson content",
  slides: [createDefaultSlide(1)],
});

export const currentSlideIndexAtom = atom<number>(0);

// Derived atoms
export const currentSlideAtom = atom((get) => {
  const lesson = get(lessonAtom);
  const currentIndex = get(currentSlideIndexAtom);
  return lesson.slides[currentIndex] || lesson.slides[0];
});

// Shared UI state atoms (used by multiple components)
export const editingComponentAtom = atom<string | null>(null);
export const previewModeAtom = atom<PreviewMode>(false);

// Removed undo/redo functionality - not needed for lesson editor

// Drag & Drop state atoms
export const draggedComponentAtom = atom<DraggedComponent | null>(null);
export const dragOverZoneAtom = atom<DragOverZone | null>(null);

// UI interaction atoms
export const selectedZoneAtom = atom<SelectedZone | null>(null);
export const showFloatingPickerAtom = atom<boolean>(false);
export const pickerPositionAtom = atom<PickerPosition>({ x: 0, y: 0 });

// Simple lesson update atom (no history management needed)
export const updateLessonAtom = atom(null, (_get, set, newLesson: Lesson) => {
  set(lessonAtom, newLesson);
});

// Change section layout atom
export const changeSectionLayoutAtom = atom(
  null,
  (
    get,
    set,
    params: {
      slideIndex: number;
      sectionIndex: number;
      newTemplate: LayoutTemplate;
      migrationOption: DataMigrationOption;
    },
  ) => {
    const currentLesson = get(lessonAtom);
    const { slideIndex, sectionIndex, newTemplate, migrationOption } = params;

    const newLesson = { ...currentLesson };
    const slide = newLesson.slides[slideIndex];
    const currentSection = slide.sections[sectionIndex];

    // Collect existing components
    const existingComponents: LayoutComponent[] = [];
    currentSection.zones.forEach((zone) => {
      existingComponents.push(...zone.components);
    });

    // Create new section with new template
    const newSection: LayoutSection = {
      id: currentSection.id,
      name: currentSection.name, // Keep existing name
      template: newTemplate,
      zones: newTemplate.zones.map((_, index) => ({
        id: `${currentSection.id}-zone-${index}`,
        components: [],
      })),
    };

    // Migrate data based on option
    if (migrationOption === "discard") {
      // Do nothing - zones are already empty
    } else if (migrationOption === "first-zone") {
      // Put all components in first zone
      if (newSection.zones.length > 0) {
        newSection.zones[0].components = existingComponents;
      }
    } else if (migrationOption === "distribute") {
      // Distribute components across zones
      existingComponents.forEach((component, index) => {
        const targetZoneIndex = index % newSection.zones.length;
        newSection.zones[targetZoneIndex].components.push(component);
      });
    }

    // Update lesson
    slide.sections[sectionIndex] = newSection;
    set(lessonAtom, newLesson);
  },
);

// Zone selection action atom
export const selectZoneAtom = atom(
  null,
  (
    get,
    set,
    zoneData: { sectionId: string; zoneId: string; position: PickerPosition },
  ) => {
    const currentSelected = get(selectedZoneAtom);

    // Toggle if same zone
    if (
      currentSelected?.sectionId === zoneData.sectionId &&
      currentSelected?.zoneId === zoneData.zoneId
    ) {
      set(selectedZoneAtom, null);
      set(showFloatingPickerAtom, false);
      return;
    }

    // Select new zone
    set(selectedZoneAtom, {
      sectionId: zoneData.sectionId,
      zoneId: zoneData.zoneId,
    });
    set(pickerPositionAtom, zoneData.position);
    set(showFloatingPickerAtom, true);
  },
);

// Close picker action atom
export const closePickerAtom = atom(null, (_get, set) => {
  set(selectedZoneAtom, null);
  set(showFloatingPickerAtom, false);
});

// Drag & Drop action atoms
export const startDragAtom = atom(
  null,
  (_get, set, dragData: DraggedComponent) => {
    set(draggedComponentAtom, dragData);
  },
);

export const dragOverAtom = atom(
  null,
  (_get, set, dragOverData: DragOverZone) => {
    set(dragOverZoneAtom, dragOverData);
  },
);

export const endDragAtom = atom(null, (_get, set) => {
  set(draggedComponentAtom, null);
  set(dragOverZoneAtom, null);
});

// Complex drag & drop operation atom
export const performDragDropAtom = atom(
  null,
  (
    get,
    set,
    dropData: {
      targetSectionId: string;
      targetZoneId: string;
      targetIndex: number;
    },
  ) => {
    const draggedComponent = get(draggedComponentAtom);
    const currentLesson = get(lessonAtom);
    const currentSlideIndex = get(currentSlideIndexAtom);

    if (!draggedComponent) return null;

    const currentSlide = currentLesson.slides[currentSlideIndex];
    const {
      sectionId: sourceSectionId,
      zoneId: sourceZoneId,
      index: sourceIndex,
    } = draggedComponent;

    const { targetSectionId, targetZoneId, targetIndex } = dropData;

    // Don't move if dropping in same position
    if (
      sourceSectionId === targetSectionId &&
      sourceZoneId === targetZoneId &&
      sourceIndex === targetIndex
    ) {
      set(draggedComponentAtom, null);
      set(dragOverZoneAtom, null);
      return null;
    }

    // Create a copy of the slide
    let updatedSlide = { ...currentSlide };
    let movedComponent: LayoutComponent | null = null;

    // Remove component from source
    updatedSlide.sections = updatedSlide.sections.map((section) => {
      if (section.id === sourceSectionId) {
        return {
          ...section,
          zones: section.zones.map((zone) => {
            if (zone.id === sourceZoneId) {
              const newComponents = [...zone.components];
              [movedComponent] = newComponents.splice(sourceIndex, 1);
              return { ...zone, components: newComponents };
            }
            return zone;
          }),
        };
      }
      return section;
    });

    // Add component to target
    if (movedComponent) {
      updatedSlide.sections = updatedSlide.sections.map((section) => {
        if (section.id === targetSectionId) {
          return {
            ...section,
            zones: section.zones.map((zone) => {
              if (zone.id === targetZoneId) {
                const newComponents = [...zone.components];
                // Adjust index if moving within same zone
                const adjustedIndex =
                  sourceSectionId === targetSectionId &&
                  sourceZoneId === targetZoneId &&
                  sourceIndex < targetIndex
                    ? targetIndex - 1
                    : targetIndex;
                newComponents.splice(adjustedIndex, 0, movedComponent!);
                return { ...zone, components: newComponents };
              }
              return zone;
            }),
          };
        }
        return section;
      });
    }

    // Update lesson with new slide
    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, slideIndex) =>
        slideIndex === currentSlideIndex ? updatedSlide : slide,
      ),
    };

    // Update lesson
    set(updateLessonAtom, newLesson);

    // Clear drag state
    set(draggedComponentAtom, null);
    set(dragOverZoneAtom, null);

    return updatedSlide;
  },
);

// Add component operation atom
export const addComponentToZoneAtom = atom(
  null,
  (
    get,
    set,
    componentData: {
      sectionId: string;
      zoneId: string;
      type: LayoutComponent["type"];
      content?: LayoutComponent["content"];
    },
  ) => {
    const currentLesson = get(lessonAtom);
    const currentSlideIndex = get(currentSlideIndexAtom);
    const currentSlide = currentLesson.slides[currentSlideIndex];

    const { sectionId, zoneId, type, content } = componentData;
    const newComponent = createNewComponent(type);
    if (content) {
      newComponent.content = content;
    }

    const updatedSlide = {
      ...currentSlide,
      sections: currentSlide.sections.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              zones: section.zones.map((zone) =>
                zone.id === zoneId
                  ? { ...zone, components: [...zone.components, newComponent] }
                  : zone,
              ),
            }
          : section,
      ),
    };

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, slideIndex) =>
        slideIndex === currentSlideIndex ? updatedSlide : slide,
      ),
    };

    // Update lesson
    set(updateLessonAtom, newLesson);

    return newLesson;
  },
);

// Update component content operation atom
export const updateComponentContentAtom = atom(
  null,
  (
    get,
    set,
    updateData: {
      sectionId: string;
      zoneId: string;
      componentId: string;
      newContent: string | object;
    },
  ) => {
    const currentLesson = get(lessonAtom);
    const currentSlideIndex = get(currentSlideIndexAtom);
    const currentSlide = currentLesson.slides[currentSlideIndex];

    const { sectionId, zoneId, componentId, newContent } = updateData;

    const updatedSlide = {
      ...currentSlide,
      sections: currentSlide.sections.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              zones: section.zones.map((zone) =>
                zone.id === zoneId
                  ? {
                      ...zone,
                      components: zone.components.map((comp) =>
                        comp.id === componentId
                          ? { ...comp, content: newContent }
                          : comp,
                      ),
                    }
                  : zone,
              ),
            }
          : section,
      ),
    };

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, slideIndex) =>
        slideIndex === currentSlideIndex ? updatedSlide : slide,
      ),
    };

    // Update lesson
    set(updateLessonAtom, newLesson);

    return newLesson;
  },
);

// Add section operation atom
export const addSectionToSlideAtom = atom(
  null,
  (get, set, template: LayoutTemplate) => {
    const currentLesson = get(lessonAtom);
    const currentSlideIndex = get(currentSlideIndexAtom);
    const currentSlide = currentLesson.slides[currentSlideIndex];

    const newSection: LayoutSection = {
      id: `section-${Date.now()}`,
      name: `${template.name} Section`,
      template: template,
      zones: template.zones.map((zone) => ({ ...zone, components: [] })),
    };

    const updatedSlide = {
      ...currentSlide,
      sections: [...currentSlide.sections, newSection],
    };

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, slideIndex) =>
        slideIndex === currentSlideIndex ? updatedSlide : slide,
      ),
    };

    // Update lesson
    set(updateLessonAtom, newLesson);

    return newLesson;
  },
);
