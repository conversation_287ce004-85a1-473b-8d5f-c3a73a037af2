import { Category, UUID } from "./app";
import { UserOther } from "./auth";
import { Lesson } from "./lessons";

export interface Course {
  id: UUID;
  name: string;
  slug: string;
  description: string;
  instructor_id: UUID;
  image_url: string;
  ordinal_index: number;
  duration: number;
  level: string;
  created_at: string;
  updated_at: string;
  instructor: UserOther;
  stats?: CourseStats | null;
  categories: Category[];
  published_at: string;
  // custom fields
  progress?: number;
  unpublished?: boolean;
}

export interface CourseDescription {
  overview: string;
  goals: string[];
}

export interface CourseStats {
  games: number;
  lessons: number;
  learners: number;
  modules: number;
  reviews: number;
  stars: number;
}

export type ProgressesStatus = "COMPLETED" | "STARTED";

export interface UserCourse {
  id: UUID;
  progresses: Record<UUID, ProgressesStatus | UUID>;
  course_id: UUID;
  user_id: UUID;
  completed_at?: string;
  continue_lesson: Lesson;
}
