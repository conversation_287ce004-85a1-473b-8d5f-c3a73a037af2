import { UUID } from "crypto";

export type Quiz = {
  id: UUID;
  question: string;
  section_id: UUID;
  skippable: boolean;
  answers: string[];
  multiple_choices: boolean;

  attempt?: QuizAttempt;
};

export type QuizAnswer = number[];

export type QuizAttempt = {
  id: UUID;
  previous_attempts: QuizAnswer[];
  user_answer: QuizAnswer;
  quiz_id: UUID;
  points: number;
  correct_answer?: QuizAnswer;
  correct?: boolean;
  explanation: string;
  correctly_answered?: boolean;
};
