import { redirect } from "@tanstack/react-router";
import { serverFnAuthHint } from "@/server/authHint";

export async function requireAuth() {
  const authHint = await serverFnAuthHint();
  console.log("authHint", authHint);

  if (authHint?.length !== 2) {
    console.log("No auth hint");
    throw redirect({
      to: "/auth",
    });
  }

  if (authHint[1] * 1000 < Date.now()) {
    console.log("Auth hint expired");
    throw redirect({
      to: "/auth",
    });
  }

  return { authHint };
}
