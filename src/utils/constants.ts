import { Course } from "../routes/dashboard/courses/types";

export const DEPARTMENTS = [
  "<PERSON><PERSON> thuật",
  "Marketing",
  "<PERSON><PERSON><PERSON> sự",
  "<PERSON><PERSON> hàng",
  "<PERSON><PERSON><PERSON> chính",
];

export const INSTRUCTORS = [
  "Nguyễn <PERSON>n <PERSON>",
  "Trần Thị B",
  "Lê Minh C",
  "Phạm Thị D",
  "Hoàng Văn E",
];

export const ITEMS_PER_PAGE_OPTIONS = [5, 10, 20, 50];

export const STATUS_COLORS = {
  published: "bg-black text-white",
  draft: "bg-blue-100 text-blue-800",
  archived: "bg-gray-100 text-gray-800",
} as const;

export const STATUS_TEXT = {
  published: "Đã xuất bản",
  draft: "Bản nháp",
  archived: "Đã lưu trữ",
} as const;

export const REQUIREMENT_COLORS = {
  company: "bg-red-100 text-red-800 border-red-200",
  department: "bg-orange-100 text-orange-800 border-orange-200",
  optional: "bg-green-100 text-green-800 border-green-200",
} as const;

export const REQUIREMENT_TEXT = {
  company: "Bắt buộc cả công ty",
  department: "Bắt buộc theo phòng ban",
  optional: "Tùy chọn/khuyến khích",
} as const;

export const MOCK_COURSES: Course[] = [
  {
    id: 1,
    title: "Cơ bản React",
    description: "Học những kiến thức cơ bản về phát triển React",
    category: "Phát triển",
    status: "published",
    completion: 100,
    students: 324,
    revenue: 3240,
    lastUpdated: "2024-01-15",
    instructor: "Nguyễn Văn A",
    createdBy: "Nguyễn Văn A",
    creationDate: "2024-01-01",
    tags: ["React", "JavaScript", "Frontend"],
    completionRate: 87,
    averageScore: 92,
    totalRatings: 156,
    averageRating: 4.8,
    department: "Kỹ thuật",
    requirement: "company",
  },
  {
    id: 2,
    title: "Thành thạo JavaScript",
    description: "Thành thạo ngôn ngữ lập trình JavaScript",
    category: "Phát triển",
    status: "published",
    completion: 100,
    students: 256,
    revenue: 2560,
    lastUpdated: "2024-01-10",
    instructor: "Trần Thị B",
    createdBy: "Trần Thị B",
    creationDate: "2023-12-15",
    tags: ["JavaScript", "Programming", "Web"],
    completionRate: 92,
    averageScore: 88,
    totalRatings: 134,
    averageRating: 4.6,
    department: "Kỹ thuật",
    requirement: "company",
  },
  {
    id: 3,
    title: "TypeScript nâng cao",
    description: "Tìm hiểu sâu về các tính năng TypeScript",
    category: "Phát triển",
    status: "draft",
    completion: 75,
    students: 0,
    revenue: 0,
    lastUpdated: "2024-01-20",
    instructor: "Lê Minh C",
    createdBy: "Lê Minh C",
    creationDate: "2024-01-15",
    tags: ["TypeScript", "JavaScript", "Types"],
    completionRate: 0,
    averageScore: 0,
    totalRatings: 0,
    averageRating: 0,
    department: "Kỹ thuật",
    requirement: "department",
  },
  {
    id: 4,
    title: "Cơ bản UI/UX Design",
    description: "Giới thiệu về thiết kế giao diện người dùng",
    category: "Thiết kế",
    status: "published",
    completion: 100,
    students: 189,
    revenue: 1890,
    lastUpdated: "2024-01-08",
    instructor: "Phạm Thị D",
    createdBy: "Phạm Thị D",
    creationDate: "2023-11-20",
    tags: ["UI", "UX", "Design", "Figma"],
    completionRate: 78,
    averageScore: 85,
    totalRatings: 89,
    averageRating: 4.3,
    department: "Marketing",
    requirement: "optional",
  },
  {
    id: 5,
    title: "Phát triển Backend Node.js",
    description: "Xây dựng ứng dụng backend có thể mở rộng",
    category: "Phát triển",
    status: "archived",
    completion: 100,
    students: 145,
    revenue: 1450,
    lastUpdated: "2023-12-20",
    instructor: "Hoàng Văn E",
    createdBy: "Hoàng Văn E",
    creationDate: "2023-10-01",
    tags: ["Node.js", "Backend", "API"],
    completionRate: 95,
    averageScore: 91,
    totalRatings: 78,
    averageRating: 4.7,
    department: "Kỹ thuật",
    requirement: "department",
  },
  {
    id: 6,
    title: "Vue.js từ cơ bản đến nâng cao",
    description: "Học framework Vue.js một cách toàn diện",
    category: "Phát triển",
    status: "published",
    completion: 100,
    students: 198,
    revenue: 1980,
    lastUpdated: "2024-02-01",
    instructor: "Ngô Thị F",
    createdBy: "Ngô Thị F",
    creationDate: "2023-12-01",
    tags: ["Vue.js", "Frontend", "SPA"],
    completionRate: 83,
    averageScore: 87,
    totalRatings: 102,
    averageRating: 4.5,
    department: "Kỹ thuật",
    requirement: "optional",
  },
  {
    id: 7,
    title: "Python cho Data Science",
    description: "Sử dụng Python để phân tích và xử lý dữ liệu",
    category: "Data Science",
    status: "published",
    completion: 100,
    students: 267,
    revenue: 2670,
    lastUpdated: "2024-01-25",
    instructor: "Đặng Minh G",
    createdBy: "Đặng Minh G",
    creationDate: "2023-11-15",
    tags: ["Python", "Data Science", "Analytics"],
    completionRate: 89,
    averageScore: 90,
    totalRatings: 145,
    averageRating: 4.6,
    department: "Kỹ thuật",
    requirement: "company",
  },
  {
    id: 8,
    title: "Machine Learning cơ bản",
    description: "Nhập môn học máy với Python",
    category: "Data Science",
    status: "draft",
    completion: 60,
    students: 0,
    revenue: 0,
    lastUpdated: "2024-02-10",
    instructor: "Vũ Thị H",
    createdBy: "Vũ Thị H",
    creationDate: "2024-01-20",
    tags: ["Machine Learning", "AI", "Python"],
    completionRate: 0,
    averageScore: 0,
    totalRatings: 0,
    averageRating: 0,
    department: "Kỹ thuật",
    requirement: "optional",
  },
  {
    id: 9,
    title: "Digital Marketing Strategy",
    description: "Chiến lược marketing số hiệu quả",
    category: "Marketing",
    status: "published",
    completion: 100,
    students: 312,
    revenue: 3120,
    lastUpdated: "2024-01-18",
    instructor: "Bùi Văn I",
    createdBy: "Bùi Văn I",
    creationDate: "2023-10-15",
    tags: ["Marketing", "Digital", "Strategy"],
    completionRate: 76,
    averageScore: 82,
    totalRatings: 167,
    averageRating: 4.2,
    department: "Marketing",
    requirement: "department",
  },
  {
    id: 10,
    title: "SEO và Content Marketing",
    description: "Tối ưu hóa công cụ tìm kiếm và marketing nội dung",
    category: "Marketing",
    status: "published",
    completion: 100,
    students: 234,
    revenue: 2340,
    lastUpdated: "2024-02-05",
    instructor: "Lý Thị K",
    createdBy: "Lý Thị K",
    creationDate: "2023-12-10",
    tags: ["SEO", "Content", "Marketing"],
    completionRate: 81,
    averageScore: 86,
    totalRatings: 123,
    averageRating: 4.4,
    department: "Marketing",
    requirement: "optional",
  },
  {
    id: 11,
    title: "Photoshop cho người mới bắt đầu",
    description: "Học cách sử dụng Adobe Photoshop từ cơ bản",
    category: "Thiết kế",
    status: "published",
    completion: 100,
    students: 156,
    revenue: 1560,
    lastUpdated: "2024-01-30",
    instructor: "Trịnh Văn L",
    createdBy: "Trịnh Văn L",
    creationDate: "2023-11-01",
    tags: ["Photoshop", "Design", "Graphics"],
    completionRate: 74,
    averageScore: 83,
    totalRatings: 67,
    averageRating: 4.1,
    department: "Marketing",
    requirement: "optional",
  },
  {
    id: 12,
    title: "Figma Design System",
    description: "Xây dựng hệ thống thiết kế với Figma",
    category: "Thiết kế",
    status: "draft",
    completion: 45,
    students: 0,
    revenue: 0,
    lastUpdated: "2024-02-12",
    instructor: "Phan Thị M",
    createdBy: "Phan Thị M",
    creationDate: "2024-02-01",
    tags: ["Figma", "Design System", "UI"],
    completionRate: 0,
    averageScore: 0,
    totalRatings: 0,
    averageRating: 0,
    department: "Marketing",
    requirement: "department",
  },
  {
    id: 13,
    title: "AWS Cloud Fundamentals",
    description: "Kiến thức cơ bản về Amazon Web Services",
    category: "Cloud Computing",
    status: "published",
    completion: 100,
    students: 178,
    revenue: 1780,
    lastUpdated: "2024-01-22",
    instructor: "Đinh Văn N",
    createdBy: "Đinh Văn N",
    creationDate: "2023-12-05",
    tags: ["AWS", "Cloud", "Infrastructure"],
    completionRate: 88,
    averageScore: 89,
    totalRatings: 94,
    averageRating: 4.5,
    department: "Kỹ thuật",
    requirement: "company",
  },
  {
    id: 14,
    title: "Docker và Kubernetes",
    description: "Container hóa và orchestration ứng dụng",
    category: "DevOps",
    status: "published",
    completion: 100,
    students: 143,
    revenue: 1430,
    lastUpdated: "2024-02-08",
    instructor: "Mai Thị O",
    createdBy: "Mai Thị O",
    creationDate: "2024-01-05",
    tags: ["Docker", "Kubernetes", "DevOps"],
    completionRate: 91,
    averageScore: 93,
    totalRatings: 76,
    averageRating: 4.7,
    department: "Kỹ thuật",
    requirement: "department",
  },
  {
    id: 15,
    title: "Cybersecurity Basics",
    description: "Bảo mật thông tin cơ bản cho developer",
    category: "Bảo mật",
    status: "archived",
    completion: 100,
    students: 89,
    revenue: 890,
    lastUpdated: "2023-11-15",
    instructor: "Cao Văn P",
    createdBy: "Cao Văn P",
    creationDate: "2023-09-01",
    tags: ["Security", "Cybersecurity", "Protection"],
    completionRate: 96,
    averageScore: 94,
    totalRatings: 45,
    averageRating: 4.8,
    department: "Kỹ thuật",
    requirement: "company",
  },
];
